<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时行情 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            padding: 20px;
        }

        .data-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .market-overview {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .index-item {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }

        .index-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .index-change {
            font-size: 1rem;
            margin-bottom: 5px;
        }

        .index-name {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .stock-item {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
            transition: transform 0.2s;
        }

        .stock-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .stock-item.negative {
            border-left-color: #dc3545;
        }

        .stock-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stock-symbol {
            font-weight: bold;
            font-size: 1.2rem;
        }

        .stock-price {
            font-weight: bold;
            font-size: 1.3rem;
        }

        .stock-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            font-size: 0.9rem;
        }

        .detail-item {
            text-align: center;
        }

        .detail-label {
            color: #666;
            margin-bottom: 5px;
        }

        .detail-value {
            font-weight: bold;
        }

        .price-up {
            color: #28a745;
        }

        .price-down {
            color: #dc3545;
        }

        .price-neutral {
            color: #6c757d;
        }

        .market-tabs {
            margin-bottom: 20px;
        }

        .market-tab {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            margin-right: 10px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .market-tab.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        .search-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .search-input {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 10px;
            padding: 12px 15px;
            width: 100%;
        }

        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            color: white;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            z-index: 1000;
            display: none;
        }

        .auto-refresh-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 10px;
        }

        .auto-refresh-toggle.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }
            
            .data-card {
                padding: 15px;
            }
            
            .stock-details {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .market-tab {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- 刷新指示器 -->
    <div class="refresh-indicator" id="refreshIndicator">
        <i class="fas fa-sync-alt fa-spin"></i> 数据更新中...
    </div>

    <div class="main-container">
        <!-- 页面标题 -->
        <div class="data-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-chart-line"></i> 实时行情数据</h2>
                    <p class="text-muted mb-0">基于akshare和东方财富的实时金融数据</p>
                </div>
                <div>
                    <a href="/dashboard" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left"></i> 返回仪表板
                    </a>
                    <button class="auto-refresh-toggle" id="autoRefreshToggle" onclick="toggleAutoRefresh()">
                        <i class="fas fa-sync-alt"></i> 自动刷新
                    </button>
                </div>
            </div>

            <!-- 市场选择 -->
            <div class="market-tabs">
                <button class="market-tab active" data-market="cn" onclick="switchMarket('cn')">
                    <i class="fas fa-flag"></i> A股市场
                </button>
                <button class="market-tab" data-market="us" onclick="switchMarket('us')">
                    <i class="fas fa-flag-usa"></i> 美股市场
                </button>
                <button class="market-tab" data-market="hk" onclick="switchMarket('hk')">
                    <i class="fas fa-building"></i> 港股市场
                </button>
                <button class="market-tab" data-market="crypto" onclick="switchMarket('crypto')">
                    <i class="fab fa-bitcoin"></i> 加密货币
                </button>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <input type="text" class="search-input" id="symbolInput" 
                               placeholder="输入股票代码，如: 000001,000002,600036">
                        <small class="text-white-50 d-block mt-1">多个代码用逗号分隔</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn-search w-100" onclick="searchStocks()">
                            <i class="fas fa-search"></i> 查询行情
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 市场概览 -->
        <div class="market-overview" id="marketOverview">
            <h4><i class="fas fa-chart-bar"></i> 市场概览</h4>
            <div class="row" id="indexList">
                <div class="col-md-4">
                    <div class="index-item">
                        <div class="index-value">加载中...</div>
                        <div class="index-change">--</div>
                        <div class="index-name">上证指数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-white" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="text-white mt-3">正在获取实时数据...</p>
        </div>

        <!-- 股票行情列表 -->
        <div class="data-card" id="stockDataContainer" style="display: none;">
            <h4><i class="fas fa-list"></i> 实时行情</h4>
            <div id="stockList">
                <!-- 动态生成股票数据 -->
            </div>
        </div>

        <!-- 数据源信息 -->
        <div class="data-card">
            <h5><i class="fas fa-database"></i> 数据源信息</h5>
            <div class="row">
                <div class="col-md-6">
                    <h6>国内数据源</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> akshare - A股实时行情</li>
                        <li><i class="fas fa-check text-success"></i> 东方财富 - 市场数据</li>
                        <li><i class="fas fa-check text-success"></i> 新浪财经 - 港股数据</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>国外数据源</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> Yahoo Finance - 美股行情</li>
                        <li><i class="fas fa-check text-success"></i> Alpha Vantage - 金融数据</li>
                        <li><i class="fas fa-check text-success"></i> Binance API - 加密货币</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/market_data.js') }}"></script>
</body>
</html>
