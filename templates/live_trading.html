<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实盘交易 - QuantTradeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .trading-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .account-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .position-card {
            background: rgba(255, 255, 255, 0.05);
            border-left: 4px solid #28a745;
        }
        
        .position-card.negative {
            border-left-color: #dc3545;
        }
        
        .order-form {
            background: rgba(255, 255, 255, 0.08);
        }
        
        .risk-alert {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        
        .metric-card {
            text-align: center;
            padding: 15px;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .order-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-filled { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        .real-time-price {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        
        .price-up { color: #28a745; }
        .price-down { color: #dc3545; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> QuantTradeX
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">仪表板</a>
                <a class="nav-link active" href="/live-trading">实盘交易</a>
                <a class="nav-link" href="/logout">退出</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 账户摘要 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="trading-card account-summary">
                    <h5><i class="fas fa-wallet"></i> 账户摘要</h5>
                    <div class="row" id="accountSummary">
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="totalValue">$0.00</div>
                                <div class="metric-label">总资产</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="cashBalance">$0.00</div>
                                <div class="metric-label">现金余额</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="positionsValue">$0.00</div>
                                <div class="metric-label">持仓市值</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="unrealizedPnl">$0.00</div>
                                <div class="metric-label">未实现盈亏</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="dayPnl">$0.00</div>
                                <div class="metric-label">当日盈亏</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="totalReturn">0.00%</div>
                                <div class="metric-label">总收益率</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 左侧：交易面板 -->
            <div class="col-md-4">
                <!-- 下单表单 -->
                <div class="trading-card order-form">
                    <h5><i class="fas fa-plus-circle"></i> 下单交易</h5>
                    <form id="orderForm">
                        <div class="mb-3">
                            <label class="form-label">股票代码</label>
                            <input type="text" class="form-control" id="symbol" placeholder="如: AAPL" required>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">交易方向</label>
                                <select class="form-select" id="side" required>
                                    <option value="buy">买入</option>
                                    <option value="sell">卖出</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">订单类型</label>
                                <select class="form-select" id="orderType" required>
                                    <option value="market">市价单</option>
                                    <option value="limit">限价单</option>
                                    <option value="stop">止损单</option>
                                    <option value="stop_limit">止损限价单</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">数量</label>
                            <input type="number" class="form-control" id="quantity" min="1" required>
                        </div>
                        
                        <div class="mb-3" id="priceField" style="display: none;">
                            <label class="form-label">价格</label>
                            <input type="number" class="form-control" id="price" step="0.01">
                        </div>
                        
                        <div class="mb-3" id="stopPriceField" style="display: none;">
                            <label class="form-label">止损价格</label>
                            <input type="number" class="form-control" id="stopPrice" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">有效期</label>
                            <select class="form-select" id="timeInForce">
                                <option value="DAY">当日有效</option>
                                <option value="GTC">撤销前有效</option>
                                <option value="IOC">立即成交或取消</option>
                                <option value="FOK">全部成交或取消</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i> 提交订单
                        </button>
                    </form>
                </div>

                <!-- 风险预警 -->
                <div class="trading-card" id="riskAlerts">
                    <h5><i class="fas fa-exclamation-triangle"></i> 风险预警</h5>
                    <div id="alertsList">
                        <p class="text-muted">暂无风险预警</p>
                    </div>
                </div>
            </div>

            <!-- 中间：持仓和订单 -->
            <div class="col-md-8">
                <!-- 持仓信息 -->
                <div class="trading-card">
                    <h5><i class="fas fa-briefcase"></i> 当前持仓</h5>
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>股票代码</th>
                                    <th>数量</th>
                                    <th>成本价</th>
                                    <th>市价</th>
                                    <th>市值</th>
                                    <th>盈亏</th>
                                    <th>盈亏率</th>
                                </tr>
                            </thead>
                            <tbody id="positionsTable">
                                <tr>
                                    <td colspan="7" class="text-center text-muted">暂无持仓</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 订单管理 -->
                <div class="trading-card">
                    <h5><i class="fas fa-list-alt"></i> 订单管理</h5>
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>订单ID</th>
                                    <th>股票代码</th>
                                    <th>方向</th>
                                    <th>类型</th>
                                    <th>数量</th>
                                    <th>价格</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="ordersTable">
                                <tr>
                                    <td colspan="9" class="text-center text-muted">暂无订单</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 绩效指标 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="trading-card">
                    <h5><i class="fas fa-chart-bar"></i> 绩效指标</h5>
                    <div class="row" id="performanceMetrics">
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="totalReturnPercent">0.00%</div>
                                <div class="metric-label">总收益率</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="annualizedReturn">0.00%</div>
                                <div class="metric-label">年化收益率</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="sharpeRatio">0.00</div>
                                <div class="metric-label">夏普比率</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="maxDrawdown">0.00%</div>
                                <div class="metric-label">最大回撤</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="winRate">0.00%</div>
                                <div class="metric-label">胜率</div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="metric-card">
                                <div class="metric-value" id="totalTrades">0</div>
                                <div class="metric-label">总交易次数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单确认模态框 -->
    <div class="modal fade" id="orderConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content bg-dark">
                <div class="modal-header">
                    <h5 class="modal-title">确认订单</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderConfirmBody">
                    <!-- 订单确认内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmOrderBtn">确认提交</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/live_trading.js') }}"></script>
</body>
</html>
