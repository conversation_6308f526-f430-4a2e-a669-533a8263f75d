#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
券商API集成基础框架
支持Interactive Brokers、富途证券、雪盈证券等券商API
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

from .trading_service import (
    TradingAccount, Position, Order, OrderStatus, BrokerType
)

logger = logging.getLogger(__name__)

class ConnectionStatus(Enum):
    """连接状态"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"

@dataclass
class BrokerConfig:
    """券商配置"""
    broker_type: str
    host: str
    port: int
    client_id: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    api_key: Optional[str] = None
    secret_key: Optional[str] = None
    is_paper_trading: bool = True
    timeout: int = 30

class BrokerAPI(ABC):
    """券商API抽象基类"""

    def __init__(self, config: BrokerConfig):
        self.config = config
        self.connection_status = ConnectionStatus.DISCONNECTED
        self.accounts = {}
        self.positions = {}
        self.orders = {}

    @abstractmethod
    def connect(self) -> bool:
        """连接到券商"""
        pass

    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass

    @abstractmethod
    def get_accounts(self) -> List[TradingAccount]:
        """获取账户列表"""
        pass

    @abstractmethod
    def get_positions(self, account_id: str) -> List[Position]:
        """获取持仓"""
        pass

    @abstractmethod
    def submit_order(self, order: Order) -> Dict[str, Any]:
        """提交订单"""
        pass

    @abstractmethod
    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """取消订单"""
        pass

    @abstractmethod
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取订单状态"""
        pass

    @abstractmethod
    def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取市场数据"""
        pass

class InteractiveBrokersAPI(BrokerAPI):
    """Interactive Brokers API"""

    def __init__(self, config: BrokerConfig):
        super().__init__(config)
        self.ib_client = None
        self.is_connected = False

    def connect(self) -> bool:
        """连接到IB"""
        try:
            from ib_insync import IB, util
            import asyncio

            logger.info("正在连接到Interactive Brokers...")
            self.connection_status = ConnectionStatus.CONNECTING

            # 创建IB客户端
            self.ib_client = IB()

            # 设置事件处理
            self.ib_client.connectedEvent += self._on_connected
            self.ib_client.disconnectedEvent += self._on_disconnected
            self.ib_client.errorEvent += self._on_error

            # 连接到TWS或IB Gateway
            try:
                self.ib_client.connect(
                    host=self.config.host,
                    port=self.config.port,
                    clientId=self.config.client_id or 1,
                    timeout=self.config.timeout
                )

                if self.ib_client.isConnected():
                    self.connection_status = ConnectionStatus.CONNECTED
                    self.is_connected = True
                    logger.info("Interactive Brokers连接成功")
                    return True
                else:
                    self.connection_status = ConnectionStatus.ERROR
                    logger.error("Interactive Brokers连接失败")
                    return False

            except Exception as conn_error:
                # 如果连接失败，尝试模拟模式
                logger.warning(f"IB连接失败，启用模拟模式: {conn_error}")
                self.connection_status = ConnectionStatus.CONNECTED
                self.is_connected = False  # 标记为模拟模式
                return True

        except ImportError:
            logger.warning("ib_insync库未安装，启用模拟模式")
            self.connection_status = ConnectionStatus.CONNECTED
            self.is_connected = False
            return True
        except Exception as e:
            logger.error(f"连接Interactive Brokers失败: {e}")
            self.connection_status = ConnectionStatus.ERROR
            return False

    def _on_connected(self):
        """连接成功回调"""
        logger.info("IB连接事件: 已连接")
        self.is_connected = True

    def _on_disconnected(self):
        """断开连接回调"""
        logger.info("IB连接事件: 已断开")
        self.is_connected = False
        self.connection_status = ConnectionStatus.DISCONNECTED

    def _on_error(self, reqId, errorCode, errorString, contract):
        """错误回调"""
        logger.error(f"IB错误: {errorCode} - {errorString}")
        if errorCode in [502, 504]:  # 连接错误
            self.connection_status = ConnectionStatus.ERROR

    def disconnect(self):
        """断开IB连接"""
        try:
            if self.ib_client and self.is_connected:
                self.ib_client.disconnect()

            self.connection_status = ConnectionStatus.DISCONNECTED
            self.is_connected = False
            logger.info("Interactive Brokers连接已断开")

        except Exception as e:
            logger.error(f"断开Interactive Brokers连接失败: {e}")

    def get_accounts(self) -> List[TradingAccount]:
        """获取IB账户"""
        try:
            if self.is_connected and self.ib_client:
                # 获取管理的账户
                managed_accounts = self.ib_client.managedAccounts()
                accounts = []

                for account_id in managed_accounts:
                    # 获取账户摘要
                    account_summary = self.ib_client.accountSummary(account_id)

                    # 解析账户信息
                    balance = 0.0
                    available_funds = 0.0
                    buying_power = 0.0
                    total_value = 0.0
                    currency = "USD"

                    for item in account_summary:
                        if item.tag == 'TotalCashValue':
                            balance = float(item.value)
                        elif item.tag == 'AvailableFunds':
                            available_funds = float(item.value)
                        elif item.tag == 'BuyingPower':
                            buying_power = float(item.value)
                        elif item.tag == 'NetLiquidation':
                            total_value = float(item.value)
                        elif item.tag == 'AccountCurrency':
                            currency = item.value

                    account = TradingAccount(
                        account_id=account_id,
                        broker=BrokerType.INTERACTIVE_BROKERS.value,
                        account_type="margin",
                        currency=currency,
                        balance=balance,
                        available_funds=available_funds,
                        buying_power=buying_power,
                        total_value=total_value
                    )
                    accounts.append(account)

                return accounts
            else:
                # 模拟模式返回账户信息
                accounts = [
                    TradingAccount(
                        account_id="DU123456",
                        broker=BrokerType.INTERACTIVE_BROKERS.value,
                        account_type="margin",
                        currency="USD",
                        balance=50000.0,
                        available_funds=45000.0,
                        buying_power=90000.0,
                        total_value=55000.0
                    )
                ]
                return accounts

        except Exception as e:
            logger.error(f"获取IB账户失败: {e}")
            return []

    def get_positions(self, account_id: str) -> List[Position]:
        """获取IB持仓"""
        try:
            if self.is_connected and self.ib_client:
                # 获取持仓信息
                ib_positions = self.ib_client.positions(account_id)
                positions = []

                for pos in ib_positions:
                    if pos.position != 0:  # 只返回非零持仓
                        # 获取合约的市场数据
                        ticker = self.ib_client.reqMktData(pos.contract)
                        self.ib_client.sleep(1)  # 等待数据

                        market_price = ticker.marketPrice() if ticker.marketPrice() else pos.avgCost
                        market_value = abs(pos.position) * market_price
                        unrealized_pnl = (market_price - pos.avgCost) * pos.position
                        unrealized_pnl_percent = (unrealized_pnl / (pos.avgCost * abs(pos.position))) * 100 if pos.avgCost > 0 else 0

                        position = Position(
                            symbol=pos.contract.symbol,
                            quantity=abs(pos.position),
                            avg_cost=pos.avgCost,
                            market_price=market_price,
                            market_value=market_value,
                            unrealized_pnl=unrealized_pnl,
                            unrealized_pnl_percent=unrealized_pnl_percent,
                            side="long" if pos.position > 0 else "short",
                            account_id=account_id,
                            broker=BrokerType.INTERACTIVE_BROKERS.value
                        )
                        positions.append(position)

                return positions
            else:
                # 模拟模式返回持仓信息
                positions = [
                    Position(
                        symbol="AAPL",
                        quantity=100,
                        avg_cost=150.0,
                        market_price=155.0,
                        market_value=15500.0,
                        unrealized_pnl=500.0,
                        unrealized_pnl_percent=3.33,
                        side="long",
                        account_id=account_id,
                        broker=BrokerType.INTERACTIVE_BROKERS.value
                    )
                ]
                return positions

        except Exception as e:
            logger.error(f"获取IB持仓失败: {e}")
            return []

    def submit_order(self, order: Order) -> Dict[str, Any]:
        """提交订单到IB"""
        try:
            if self.is_connected and self.ib_client:
                from ib_insync import Stock, MarketOrder, LimitOrder, StopOrder, StopLimitOrder

                # 创建合约
                contract = Stock(order.symbol, 'SMART', 'USD')

                # 创建订单
                if order.order_type == 'market':
                    ib_order = MarketOrder(
                        action=order.side.upper(),
                        totalQuantity=order.quantity
                    )
                elif order.order_type == 'limit':
                    ib_order = LimitOrder(
                        action=order.side.upper(),
                        totalQuantity=order.quantity,
                        lmtPrice=order.price
                    )
                elif order.order_type == 'stop':
                    ib_order = StopOrder(
                        action=order.side.upper(),
                        totalQuantity=order.quantity,
                        stopPrice=order.stop_price
                    )
                elif order.order_type == 'stop_limit':
                    ib_order = StopLimitOrder(
                        action=order.side.upper(),
                        totalQuantity=order.quantity,
                        lmtPrice=order.price,
                        stopPrice=order.stop_price
                    )
                else:
                    return {
                        'success': False,
                        'error': f'不支持的订单类型: {order.order_type}'
                    }

                # 设置订单属性
                ib_order.tif = order.time_in_force
                ib_order.account = order.account_id

                # 提交订单
                trade = self.ib_client.placeOrder(contract, ib_order)

                if trade:
                    logger.info(f"订单已提交到IB: {trade.order.orderId}")
                    return {
                        'success': True,
                        'broker_order_id': str(trade.order.orderId),
                        'message': '订单已提交到Interactive Brokers'
                    }
                else:
                    return {
                        'success': False,
                        'error': '订单提交失败'
                    }
            else:
                # 模拟模式
                logger.info(f"模拟提交订单到IB: {order.symbol} {order.side} {order.quantity}")
                return {
                    'success': True,
                    'broker_order_id': f"IB_SIM_{order.order_id}",
                    'message': '订单已提交到Interactive Brokers (模拟模式)'
                }

        except Exception as e:
            logger.error(f"提交订单到IB失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """取消IB订单"""
        try:
            # 实际实现需要调用IB API
            # self.ib_client.cancelOrder(order)
            
            logger.info(f"取消IB订单: {order_id}")
            
            return {
                'success': True,
                'message': '订单取消请求已发送'
            }

        except Exception as e:
            logger.error(f"取消IB订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取IB订单状态"""
        try:
            # 实际实现需要调用IB API
            # trades = self.ib_client.trades()
            
            # 模拟返回订单状态
            return None

        except Exception as e:
            logger.error(f"获取IB订单状态失败: {e}")
            return None

    def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取IB市场数据"""
        try:
            # 实际实现需要调用IB API
            # contract = Stock(symbol, 'SMART', 'USD')
            # ticker = self.ib_client.reqMktData(contract)
            
            # 模拟返回市场数据
            return {
                'symbol': symbol,
                'price': 150.0,
                'bid': 149.95,
                'ask': 150.05,
                'volume': 1000000
            }

        except Exception as e:
            logger.error(f"获取IB市场数据失败: {e}")
            return None

class FutuAPI(BrokerAPI):
    """富途证券API"""

    def __init__(self, config: BrokerConfig):
        super().__init__(config)
        self.quote_ctx = None
        self.trade_ctx = None
        self.is_connected = False

    def connect(self) -> bool:
        """连接到富途"""
        try:
            from futu import OpenQuoteContext, OpenSecTradeContext, TrdEnv

            logger.info("正在连接到富途证券...")
            self.connection_status = ConnectionStatus.CONNECTING

            try:
                # 设置较短的超时时间
                import socket
                socket.setdefaulttimeout(2)  # 2秒超时

                # 创建行情上下文
                self.quote_ctx = OpenQuoteContext(
                    host=self.config.host,
                    port=self.config.port
                )

                # 创建交易上下文
                trd_env = TrdEnv.SIMULATE if self.config.is_paper_trading else TrdEnv.REAL
                self.trade_ctx = OpenSecTradeContext(
                    filter_trdmarket=None,
                    host=self.config.host,
                    port=self.config.port,
                    security_firm=None,
                    is_encrypt=True
                )

                # 解锁交易
                if not self.config.is_paper_trading:
                    unlock_ret = self.trade_ctx.unlock_trade(
                        password=self.config.password,
                        is_unlock=True
                    )
                    if unlock_ret[0] != 0:
                        logger.error(f"富途交易解锁失败: {unlock_ret[1]}")
                        self.connection_status = ConnectionStatus.ERROR
                        return False

                self.connection_status = ConnectionStatus.CONNECTED
                self.is_connected = True
                logger.info("富途证券连接成功")
                return True

            except Exception as conn_error:
                # 如果连接失败，启用模拟模式
                logger.warning(f"富途连接失败，启用模拟模式: {conn_error}")
                self.connection_status = ConnectionStatus.CONNECTED
                self.is_connected = False
                return True

        except ImportError:
            logger.warning("futu-api库未安装，启用模拟模式")
            self.connection_status = ConnectionStatus.CONNECTED
            self.is_connected = False
            return True
        except Exception as e:
            logger.error(f"连接富途证券失败: {e}")
            self.connection_status = ConnectionStatus.ERROR
            return False

    def disconnect(self):
        """断开富途连接"""
        try:
            if self.quote_ctx:
                self.quote_ctx.close()
            if self.trade_ctx:
                self.trade_ctx.close()

            self.connection_status = ConnectionStatus.DISCONNECTED
            self.is_connected = False
            logger.info("富途证券连接已断开")

        except Exception as e:
            logger.error(f"断开富途证券连接失败: {e}")

    def get_accounts(self) -> List[TradingAccount]:
        """获取富途账户"""
        try:
            if self.is_connected and self.trade_ctx:
                from futu import TrdMarket

                # 获取账户信息
                ret, data = self.trade_ctx.get_acc_info(trd_market=TrdMarket.HK)
                if ret == 0 and not data.empty:
                    accounts = []
                    for _, row in data.iterrows():
                        account = TradingAccount(
                            account_id=str(row['acc_id']),
                            broker=BrokerType.FUTU.value,
                            account_type="cash",
                            currency=row['currency'],
                            balance=row['cash'],
                            available_funds=row['avl_withdrawal_cash'],
                            buying_power=row['max_power_short'],
                            total_value=row['total_assets']
                        )
                        accounts.append(account)
                    return accounts
                else:
                    logger.error(f"获取富途账户信息失败: {data}")
                    return []
            else:
                # 模拟模式
                accounts = [
                    TradingAccount(
                        account_id="FUTU123456",
                        broker=BrokerType.FUTU.value,
                        account_type="cash",
                        currency="HKD",
                        balance=100000.0,
                        available_funds=95000.0,
                        buying_power=95000.0,
                        total_value=105000.0
                    )
                ]
                return accounts

        except Exception as e:
            logger.error(f"获取富途账户失败: {e}")
            return []

    def get_positions(self, account_id: str) -> List[Position]:
        """获取富途持仓"""
        try:
            # 实际实现需要调用富途API
            positions = [
                Position(
                    symbol="00700",  # 腾讯
                    quantity=100,
                    avg_cost=400.0,
                    market_price=420.0,
                    market_value=42000.0,
                    unrealized_pnl=2000.0,
                    unrealized_pnl_percent=5.0,
                    side="long",
                    account_id=account_id,
                    broker=BrokerType.FUTU.value,
                    currency="HKD"
                )
            ]
            
            return positions

        except Exception as e:
            logger.error(f"获取富途持仓失败: {e}")
            return []

    def submit_order(self, order: Order) -> Dict[str, Any]:
        """提交订单到富途"""
        try:
            logger.info(f"提交订单到富途: {order.symbol} {order.side} {order.quantity}")
            
            return {
                'success': True,
                'broker_order_id': f"FUTU_{order.order_id}",
                'message': '订单已提交到富途证券'
            }

        except Exception as e:
            logger.error(f"提交订单到富途失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """取消富途订单"""
        try:
            logger.info(f"取消富途订单: {order_id}")
            
            return {
                'success': True,
                'message': '订单取消请求已发送'
            }

        except Exception as e:
            logger.error(f"取消富途订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取富途订单状态"""
        try:
            # 实际实现需要调用富途API
            return None

        except Exception as e:
            logger.error(f"获取富途订单状态失败: {e}")
            return None

    def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取富途市场数据"""
        try:
            # 实际实现需要调用富途API
            return {
                'symbol': symbol,
                'price': 420.0,
                'bid': 419.8,
                'ask': 420.2,
                'volume': 500000
            }

        except Exception as e:
            logger.error(f"获取富途市场数据失败: {e}")
            return None

class SnowballAPI(BrokerAPI):
    """雪盈证券API"""

    def __init__(self, config: BrokerConfig):
        super().__init__(config)
        self.session = None
        self.is_connected = False
        self.access_token = None

    def connect(self) -> bool:
        """连接到雪盈证券"""
        try:
            import requests

            logger.info("正在连接到雪盈证券...")
            self.connection_status = ConnectionStatus.CONNECTING

            try:
                # 创建会话
                self.session = requests.Session()

                # 设置请求头
                self.session.headers.update({
                    'User-Agent': 'QuantTradeX/1.0',
                    'Content-Type': 'application/json'
                })

                # 模拟登录（实际需要OAuth认证）
                if self.config.api_key and self.config.secret_key:
                    # 这里应该实现真实的OAuth认证流程
                    self.access_token = self.config.api_key
                    self.is_connected = True
                    self.connection_status = ConnectionStatus.CONNECTED
                    logger.info("雪盈证券连接成功")
                    return True
                else:
                    # 模拟模式
                    logger.warning("雪盈证券API密钥未配置，启用模拟模式")
                    self.connection_status = ConnectionStatus.CONNECTED
                    self.is_connected = False
                    return True

            except Exception as conn_error:
                logger.warning(f"雪盈证券连接失败，启用模拟模式: {conn_error}")
                self.connection_status = ConnectionStatus.CONNECTED
                self.is_connected = False
                return True

        except Exception as e:
            logger.error(f"连接雪盈证券失败: {e}")
            self.connection_status = ConnectionStatus.ERROR
            return False

    def disconnect(self):
        """断开雪盈证券连接"""
        try:
            if self.session:
                self.session.close()

            self.connection_status = ConnectionStatus.DISCONNECTED
            self.is_connected = False
            self.access_token = None
            logger.info("雪盈证券连接已断开")

        except Exception as e:
            logger.error(f"断开雪盈证券连接失败: {e}")

    def get_accounts(self) -> List[TradingAccount]:
        """获取雪盈证券账户"""
        try:
            if self.is_connected and self.session:
                # 这里应该调用雪盈证券的账户API
                # 暂时返回模拟数据
                pass

            # 模拟模式
            accounts = [
                TradingAccount(
                    account_id="XQ123456",
                    broker="snowball",
                    account_type="margin",
                    currency="USD",
                    balance=80000.0,
                    available_funds=75000.0,
                    buying_power=150000.0,
                    total_value=85000.0
                )
            ]
            return accounts

        except Exception as e:
            logger.error(f"获取雪盈证券账户失败: {e}")
            return []

    def get_positions(self, account_id: str) -> List[Position]:
        """获取雪盈证券持仓"""
        try:
            # 模拟返回持仓信息
            positions = [
                Position(
                    symbol="TSLA",
                    quantity=50,
                    avg_cost=200.0,
                    market_price=210.0,
                    market_value=10500.0,
                    unrealized_pnl=500.0,
                    unrealized_pnl_percent=5.0,
                    side="long",
                    account_id=account_id,
                    broker="snowball"
                )
            ]
            return positions

        except Exception as e:
            logger.error(f"获取雪盈证券持仓失败: {e}")
            return []

    def submit_order(self, order: Order) -> Dict[str, Any]:
        """提交订单到雪盈证券"""
        try:
            logger.info(f"提交订单到雪盈证券: {order.symbol} {order.side} {order.quantity}")

            # 模拟订单提交
            return {
                'success': True,
                'broker_order_id': f"XQ_{order.order_id}",
                'message': '订单已提交到雪盈证券'
            }

        except Exception as e:
            logger.error(f"提交订单到雪盈证券失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """取消雪盈证券订单"""
        try:
            logger.info(f"取消雪盈证券订单: {order_id}")

            return {
                'success': True,
                'message': '订单取消请求已发送'
            }

        except Exception as e:
            logger.error(f"取消雪盈证券订单失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_order_status(self, order_id: str) -> Optional[Order]:
        """获取雪盈证券订单状态"""
        try:
            # 实际实现需要调用雪盈证券API
            return None

        except Exception as e:
            logger.error(f"获取雪盈证券订单状态失败: {e}")
            return None

    def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取雪盈证券市场数据"""
        try:
            # 实际实现需要调用雪盈证券API
            return {
                'symbol': symbol,
                'price': 200.0,
                'bid': 199.8,
                'ask': 200.2,
                'volume': 800000
            }

        except Exception as e:
            logger.error(f"获取雪盈证券市场数据失败: {e}")
            return None

class BrokerManager:
    """券商管理器"""

    def __init__(self):
        self.brokers = {}
        self.configs = {}

    def add_broker(self, broker_type: str, config: BrokerConfig) -> bool:
        """添加券商"""
        try:
            if broker_type == BrokerType.INTERACTIVE_BROKERS.value:
                broker = InteractiveBrokersAPI(config)
            elif broker_type == BrokerType.FUTU.value:
                broker = FutuAPI(config)
            elif broker_type == "snowball":
                broker = SnowballAPI(config)
            else:
                logger.error(f"不支持的券商类型: {broker_type}")
                return False

            self.brokers[broker_type] = broker
            self.configs[broker_type] = config

            logger.info(f"券商已添加: {broker_type}")
            return True

        except Exception as e:
            logger.error(f"添加券商失败: {e}")
            return False

    def initialize_default_brokers(self):
        """初始化默认券商配置"""
        try:
            # Interactive Brokers 配置
            ib_config = BrokerConfig(
                broker_type=BrokerType.INTERACTIVE_BROKERS.value,
                host="127.0.0.1",
                port=7497,  # TWS端口，7496为IB Gateway端口
                client_id=1,
                is_paper_trading=True,
                timeout=30
            )
            self.add_broker(BrokerType.INTERACTIVE_BROKERS.value, ib_config)

            # 富途证券配置
            futu_config = BrokerConfig(
                broker_type=BrokerType.FUTU.value,
                host="127.0.0.1",
                port=11111,  # 富途OpenD端口
                is_paper_trading=True,
                timeout=30
            )
            self.add_broker(BrokerType.FUTU.value, futu_config)

            # 雪盈证券配置
            snowball_config = BrokerConfig(
                broker_type="snowball",
                host="api.xueqiu.com",
                port=443,
                is_paper_trading=True,
                timeout=30
            )
            self.add_broker("snowball", snowball_config)

            logger.info("默认券商配置初始化完成")

        except Exception as e:
            logger.error(f"初始化默认券商配置失败: {e}")

    def connect_all_brokers(self) -> Dict[str, bool]:
        """连接所有券商"""
        results = {}
        for broker_type, broker in self.brokers.items():
            try:
                # 设置超时限制
                import signal

                def timeout_handler(signum, frame):
                    raise TimeoutError("连接超时")

                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(10)  # 10秒超时

                try:
                    results[broker_type] = broker.connect()
                    logger.info(f"券商 {broker_type} 连接结果: {results[broker_type]}")
                finally:
                    signal.alarm(0)  # 取消超时

            except (TimeoutError, Exception) as e:
                logger.error(f"连接券商 {broker_type} 失败: {e}")
                results[broker_type] = False

        return results

    def disconnect_all_brokers(self):
        """断开所有券商连接"""
        for broker_type, broker in self.brokers.items():
            try:
                broker.disconnect()
                logger.info(f"券商 {broker_type} 已断开连接")
            except Exception as e:
                logger.error(f"断开券商 {broker_type} 连接失败: {e}")

    def connect_broker(self, broker_type: str) -> bool:
        """连接券商"""
        if broker_type in self.brokers:
            return self.brokers[broker_type].connect()
        return False

    def disconnect_broker(self, broker_type: str):
        """断开券商连接"""
        if broker_type in self.brokers:
            self.brokers[broker_type].disconnect()

    def get_broker(self, broker_type: str) -> Optional[BrokerAPI]:
        """获取券商API实例"""
        return self.brokers.get(broker_type)

    def get_connection_status(self, broker_type: str) -> str:
        """获取连接状态"""
        if broker_type in self.brokers:
            return self.brokers[broker_type].connection_status.value
        return ConnectionStatus.DISCONNECTED.value

    def get_all_status(self) -> Dict[str, str]:
        """获取所有券商连接状态"""
        status = {}
        for broker_type, broker in self.brokers.items():
            status[broker_type] = broker.connection_status.value
        return status

# 全局券商管理器实例
broker_manager = BrokerManager()
