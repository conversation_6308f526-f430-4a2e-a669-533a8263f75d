#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
风险管理模块
负责实时风控、风险监控、风险预警等功能
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import time

from .trading_service import (
    TradingAccount, Position, Order, OrderStatus,
    trading_service
)

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """预警类型"""
    POSITION_LIMIT = "position_limit"
    DRAWDOWN = "drawdown"
    CONCENTRATION = "concentration"
    LEVERAGE = "leverage"
    DAILY_LOSS = "daily_loss"
    VOLATILITY = "volatility"

@dataclass
class RiskLimit:
    """风险限制"""
    limit_type: str
    limit_value: float
    current_value: float
    threshold_percent: float = 80.0  # 预警阈值百分比
    is_breached: bool = False
    last_check: str = ""

    def __post_init__(self):
        if not self.last_check:
            self.last_check = datetime.now().isoformat()

@dataclass
class RiskAlert:
    """风险预警"""
    alert_id: str
    account_id: str
    alert_type: str
    risk_level: str
    message: str
    current_value: float
    limit_value: float
    is_active: bool = True
    created_at: str = ""
    resolved_at: Optional[str] = None

    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()

@dataclass
class RiskMetrics:
    """风险指标"""
    account_id: str
    var_1d: float           # 1日风险价值
    var_5d: float           # 5日风险价值
    max_drawdown: float     # 最大回撤
    current_drawdown: float # 当前回撤
    volatility: float       # 波动率
    beta: float            # 贝塔值
    concentration_risk: float  # 集中度风险
    leverage_ratio: float   # 杠杆比率
    daily_pnl: float       # 当日盈亏
    updated_at: str = ""

    def __post_init__(self):
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()

class RiskManager:
    """风险管理器"""

    def __init__(self):
        self.trading_service = trading_service
        self.risk_limits = {}       # 风险限制
        self.active_alerts = {}     # 活跃预警
        self.risk_metrics = {}      # 风险指标
        self.alert_listeners = []   # 预警监听器
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 初始化默认风险限制
        self._init_default_limits()

    def _init_default_limits(self):
        """初始化默认风险限制"""
        default_limits = {
            'max_position_percent': 20.0,      # 单个持仓不超过总资产20%
            'max_sector_percent': 30.0,        # 单个行业不超过总资产30%
            'max_daily_loss_percent': 5.0,     # 日内最大亏损5%
            'max_drawdown_percent': 15.0,      # 最大回撤15%
            'max_leverage_ratio': 2.0,         # 最大杠杆比率2倍
            'max_var_percent': 3.0,            # VaR不超过总资产3%
            'min_cash_percent': 5.0            # 最低现金比例5%
        }
        
        for account_id in self.trading_service.accounts:
            self.risk_limits[account_id] = {}
            for limit_type, limit_value in default_limits.items():
                self.risk_limits[account_id][limit_type] = RiskLimit(
                    limit_type=limit_type,
                    limit_value=limit_value,
                    current_value=0.0
                )

    def start_monitoring(self):
        """启动风险监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("风险监控已启动")

    def stop_monitoring(self):
        """停止风险监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("风险监控已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                for account_id in self.trading_service.accounts:
                    # 更新风险指标
                    self._update_risk_metrics(account_id)
                    
                    # 检查风险限制
                    self._check_risk_limits(account_id)
                    
                    # 检查预警条件
                    self._check_alert_conditions(account_id)
                
                # 等待下次检查
                time.sleep(30)  # 30秒检查一次
                
            except Exception as e:
                logger.error(f"风险监控循环错误: {e}")
                time.sleep(60)

    def _update_risk_metrics(self, account_id: str):
        """更新风险指标"""
        try:
            account = self.trading_service.accounts[account_id]
            positions = self.trading_service.positions.get(account_id, {})
            
            # 计算各项风险指标
            var_1d, var_5d = self._calculate_var(account_id)
            max_drawdown = self._calculate_max_drawdown(account_id)
            current_drawdown = self._calculate_current_drawdown(account_id)
            volatility = self._calculate_portfolio_volatility(account_id)
            beta = self._calculate_portfolio_beta(account_id)
            concentration_risk = self._calculate_concentration_risk(account_id)
            leverage_ratio = self._calculate_leverage_ratio(account_id)
            daily_pnl = self._calculate_daily_pnl(account_id)
            
            metrics = RiskMetrics(
                account_id=account_id,
                var_1d=var_1d,
                var_5d=var_5d,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                volatility=volatility,
                beta=beta,
                concentration_risk=concentration_risk,
                leverage_ratio=leverage_ratio,
                daily_pnl=daily_pnl
            )
            
            self.risk_metrics[account_id] = metrics

        except Exception as e:
            logger.error(f"更新风险指标失败: {e}")

    def _check_risk_limits(self, account_id: str):
        """检查风险限制"""
        try:
            if account_id not in self.risk_limits:
                return
            
            account = self.trading_service.accounts[account_id]
            positions = self.trading_service.positions.get(account_id, {})
            limits = self.risk_limits[account_id]
            
            # 检查单个持仓比例
            for symbol, position in positions.items():
                position_percent = (position.market_value / account.total_value) * 100
                limit = limits.get('max_position_percent')
                if limit:
                    limit.current_value = position_percent
                    if position_percent > limit.limit_value:
                        limit.is_breached = True
                        self._create_alert(
                            account_id, 
                            AlertType.POSITION_LIMIT.value,
                            f"持仓 {symbol} 超过限制: {position_percent:.1f}% > {limit.limit_value:.1f}%",
                            position_percent,
                            limit.limit_value
                        )
            
            # 检查日内亏损
            daily_loss_percent = abs(self.risk_metrics.get(account_id, RiskMetrics(account_id, 0, 0, 0, 0, 0, 0, 0, 0, 0)).daily_pnl / account.total_value) * 100
            limit = limits.get('max_daily_loss_percent')
            if limit:
                limit.current_value = daily_loss_percent
                if daily_loss_percent > limit.limit_value:
                    limit.is_breached = True
                    self._create_alert(
                        account_id,
                        AlertType.DAILY_LOSS.value,
                        f"日内亏损超过限制: {daily_loss_percent:.1f}% > {limit.limit_value:.1f}%",
                        daily_loss_percent,
                        limit.limit_value
                    )
            
            # 检查回撤
            current_drawdown = self.risk_metrics.get(account_id, RiskMetrics(account_id, 0, 0, 0, 0, 0, 0, 0, 0, 0)).current_drawdown
            limit = limits.get('max_drawdown_percent')
            if limit:
                limit.current_value = current_drawdown * 100
                if current_drawdown * 100 > limit.limit_value:
                    limit.is_breached = True
                    self._create_alert(
                        account_id,
                        AlertType.DRAWDOWN.value,
                        f"回撤超过限制: {current_drawdown*100:.1f}% > {limit.limit_value:.1f}%",
                        current_drawdown * 100,
                        limit.limit_value
                    )

        except Exception as e:
            logger.error(f"检查风险限制失败: {e}")

    def _check_alert_conditions(self, account_id: str):
        """检查预警条件"""
        try:
            # 检查集中度风险
            concentration = self._calculate_concentration_risk(account_id)
            if concentration > 0.4:  # 40%集中度预警
                self._create_alert(
                    account_id,
                    AlertType.CONCENTRATION.value,
                    f"投资组合集中度过高: {concentration*100:.1f}%",
                    concentration * 100,
                    40.0
                )
            
            # 检查波动率
            volatility = self._calculate_portfolio_volatility(account_id)
            if volatility > 0.3:  # 30%年化波动率预警
                self._create_alert(
                    account_id,
                    AlertType.VOLATILITY.value,
                    f"投资组合波动率过高: {volatility*100:.1f}%",
                    volatility * 100,
                    30.0
                )

        except Exception as e:
            logger.error(f"检查预警条件失败: {e}")

    def _create_alert(self, account_id: str, alert_type: str, message: str, current_value: float, limit_value: float):
        """创建风险预警"""
        try:
            import uuid
            
            # 检查是否已存在相同类型的活跃预警
            existing_alert = None
            for alert in self.active_alerts.values():
                if (alert.account_id == account_id and 
                    alert.alert_type == alert_type and 
                    alert.is_active):
                    existing_alert = alert
                    break
            
            if existing_alert:
                # 更新现有预警
                existing_alert.current_value = current_value
                existing_alert.message = message
            else:
                # 创建新预警
                alert_id = f"ALERT_{uuid.uuid4().hex[:8].upper()}"
                
                # 确定风险等级
                risk_level = RiskLevel.MEDIUM.value
                if current_value > limit_value * 1.5:
                    risk_level = RiskLevel.CRITICAL.value
                elif current_value > limit_value * 1.2:
                    risk_level = RiskLevel.HIGH.value
                
                alert = RiskAlert(
                    alert_id=alert_id,
                    account_id=account_id,
                    alert_type=alert_type,
                    risk_level=risk_level,
                    message=message,
                    current_value=current_value,
                    limit_value=limit_value
                )
                
                self.active_alerts[alert_id] = alert
                
                # 通知监听器
                self._notify_alert_listeners(alert)
                
                logger.warning(f"风险预警: {message}")

        except Exception as e:
            logger.error(f"创建风险预警失败: {e}")

    def _calculate_var(self, account_id: str) -> Tuple[float, float]:
        """计算风险价值（VaR）"""
        try:
            # 简化的VaR计算，实际应使用历史模拟法或蒙特卡洛法
            account = self.trading_service.accounts[account_id]
            volatility = self._calculate_portfolio_volatility(account_id)
            
            # 假设正态分布，95%置信度
            var_1d = account.total_value * volatility * 1.65 / (252 ** 0.5)
            var_5d = account.total_value * volatility * 1.65 / ((252/5) ** 0.5)
            
            return var_1d, var_5d

        except Exception as e:
            logger.error(f"计算VaR失败: {e}")
            return 0.0, 0.0

    def _calculate_max_drawdown(self, account_id: str) -> float:
        """计算最大回撤"""
        try:
            # 这里应该从历史净值数据计算，暂时返回模拟值
            return 0.08  # 8%

        except Exception as e:
            logger.error(f"计算最大回撤失败: {e}")
            return 0.0

    def _calculate_current_drawdown(self, account_id: str) -> float:
        """计算当前回撤"""
        try:
            # 这里应该从历史净值数据计算，暂时返回模拟值
            return 0.03  # 3%

        except Exception as e:
            logger.error(f"计算当前回撤失败: {e}")
            return 0.0

    def _calculate_portfolio_volatility(self, account_id: str) -> float:
        """计算投资组合波动率"""
        try:
            # 简化计算，实际应考虑相关性矩阵
            positions = self.trading_service.positions.get(account_id, {})
            if not positions:
                return 0.0
            
            # 假设平均波动率为20%
            return 0.20

        except Exception as e:
            logger.error(f"计算投资组合波动率失败: {e}")
            return 0.0

    def _calculate_portfolio_beta(self, account_id: str) -> float:
        """计算投资组合贝塔值"""
        try:
            # 简化计算，实际应该根据各持仓的贝塔值加权计算
            return 1.0

        except Exception as e:
            logger.error(f"计算投资组合贝塔值失败: {e}")
            return 1.0

    def _calculate_concentration_risk(self, account_id: str) -> float:
        """计算集中度风险"""
        try:
            account = self.trading_service.accounts[account_id]
            positions = self.trading_service.positions.get(account_id, {})
            
            if not positions or account.total_value <= 0:
                return 0.0
            
            # 计算最大持仓比例
            max_position_ratio = 0.0
            for position in positions.values():
                ratio = position.market_value / account.total_value
                if ratio > max_position_ratio:
                    max_position_ratio = ratio
            
            return max_position_ratio

        except Exception as e:
            logger.error(f"计算集中度风险失败: {e}")
            return 0.0

    def _calculate_leverage_ratio(self, account_id: str) -> float:
        """计算杠杆比率"""
        try:
            account = self.trading_service.accounts[account_id]
            positions = self.trading_service.positions.get(account_id, {})
            
            total_position_value = sum(pos.market_value for pos in positions.values())
            
            if account.balance <= 0:
                return 0.0
            
            return total_position_value / account.balance

        except Exception as e:
            logger.error(f"计算杠杆比率失败: {e}")
            return 0.0

    def _calculate_daily_pnl(self, account_id: str) -> float:
        """计算当日盈亏"""
        try:
            # 这里应该从交易记录和持仓变化计算，暂时返回模拟值
            import random
            account = self.trading_service.accounts[account_id]
            return account.total_value * random.uniform(-0.02, 0.02)

        except Exception as e:
            logger.error(f"计算当日盈亏失败: {e}")
            return 0.0

    def _notify_alert_listeners(self, alert: RiskAlert):
        """通知预警监听器"""
        for listener in self.alert_listeners:
            try:
                listener(alert)
            except Exception as e:
                logger.error(f"通知预警监听器失败: {e}")

    def get_risk_metrics(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取风险指标"""
        if account_id in self.risk_metrics:
            return asdict(self.risk_metrics[account_id])
        return None

    def get_active_alerts(self, account_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取活跃预警"""
        alerts = []
        for alert in self.active_alerts.values():
            if alert.is_active and (not account_id or alert.account_id == account_id):
                alerts.append(asdict(alert))
        
        # 按创建时间倒序排列
        alerts.sort(key=lambda x: x['created_at'], reverse=True)
        return alerts

    def resolve_alert(self, alert_id: str) -> bool:
        """解决预警"""
        try:
            if alert_id in self.active_alerts:
                alert = self.active_alerts[alert_id]
                alert.is_active = False
                alert.resolved_at = datetime.now().isoformat()
                logger.info(f"预警已解决: {alert_id}")
                return True
            return False

        except Exception as e:
            logger.error(f"解决预警失败: {e}")
            return False

    def add_alert_listener(self, listener):
        """添加预警监听器"""
        self.alert_listeners.append(listener)

    def remove_alert_listener(self, listener):
        """移除预警监听器"""
        if listener in self.alert_listeners:
            self.alert_listeners.remove(listener)

# 全局风险管理器实例
risk_manager = RiskManager()
