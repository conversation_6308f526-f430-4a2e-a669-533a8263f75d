#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
市场配置管理
分离国内外市场的不同配置和接口
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class MarketRegion(Enum):
    """市场区域"""
    US = "us"           # 美股
    CN = "cn"           # A股
    HK = "hk"           # 港股
    CRYPTO = "crypto"   # 加密货币

class TradingSession(Enum):
    """交易时段"""
    PRE_MARKET = "pre_market"       # 盘前
    REGULAR = "regular"             # 正常交易
    AFTER_HOURS = "after_hours"     # 盘后
    CLOSED = "closed"               # 休市

@dataclass
class MarketConfig:
    """市场配置"""
    region: str
    name: str
    currency: str
    timezone: str
    trading_hours: Dict[str, str]
    holidays: List[str]
    min_order_size: float
    tick_size: float
    commission_rate: float
    data_providers: List[str]
    broker_apis: List[str]
    supported_order_types: List[str]

class MarketConfigManager:
    """市场配置管理器"""

    def __init__(self):
        self.configs = self._initialize_market_configs()

    def _initialize_market_configs(self) -> Dict[str, MarketConfig]:
        """初始化市场配置"""
        configs = {}

        # 美股配置
        configs[MarketRegion.US.value] = MarketConfig(
            region=MarketRegion.US.value,
            name="美国股票市场",
            currency="USD",
            timezone="America/New_York",
            trading_hours={
                "pre_market": "04:00-09:30",
                "regular": "09:30-16:00",
                "after_hours": "16:00-20:00"
            },
            holidays=[
                "2024-01-01",  # 新年
                "2024-01-15",  # 马丁路德金日
                "2024-02-19",  # 总统日
                "2024-03-29",  # 耶稣受难日
                "2024-05-27",  # 阵亡将士纪念日
                "2024-06-19",  # 六月节
                "2024-07-04",  # 独立日
                "2024-09-02",  # 劳动节
                "2024-11-28",  # 感恩节
                "2024-12-25"   # 圣诞节
            ],
            min_order_size=1.0,
            tick_size=0.01,
            commission_rate=0.0005,  # 0.05%
            data_providers=["alpha_vantage", "finnhub", "polygon", "yahoo"],
            broker_apis=["interactive_brokers", "alpaca", "td_ameritrade"],
            supported_order_types=["market", "limit", "stop", "stop_limit", "trailing_stop"]
        )

        # A股配置
        configs[MarketRegion.CN.value] = MarketConfig(
            region=MarketRegion.CN.value,
            name="中国A股市场",
            currency="CNY",
            timezone="Asia/Shanghai",
            trading_hours={
                "morning": "09:30-11:30",
                "afternoon": "13:00-15:00"
            },
            holidays=[
                "2024-01-01",  # 元旦
                "2024-02-10",  # 春节
                "2024-02-11",
                "2024-02-12",
                "2024-02-13",
                "2024-02-14",
                "2024-02-15",
                "2024-02-16",
                "2024-02-17",
                "2024-04-04",  # 清明节
                "2024-04-05",
                "2024-04-06",
                "2024-05-01",  # 劳动节
                "2024-05-02",
                "2024-05-03",
                "2024-06-10",  # 端午节
                "2024-09-15",  # 中秋节
                "2024-09-16",
                "2024-09-17",
                "2024-10-01",  # 国庆节
                "2024-10-02",
                "2024-10-03",
                "2024-10-04",
                "2024-10-05",
                "2024-10-06",
                "2024-10-07"
            ],
            min_order_size=100.0,  # 最小100股
            tick_size=0.01,
            commission_rate=0.0003,  # 0.03%
            data_providers=["tushare", "akshare", "eastmoney", "sina"],
            broker_apis=["ctp", "xtp", "ths", "tongdaxin"],
            supported_order_types=["market", "limit"]
        )

        # 港股配置
        configs[MarketRegion.HK.value] = MarketConfig(
            region=MarketRegion.HK.value,
            name="香港股票市场",
            currency="HKD",
            timezone="Asia/Hong_Kong",
            trading_hours={
                "morning": "09:30-12:00",
                "afternoon": "13:00-16:00"
            },
            holidays=[
                "2024-01-01",  # 新年
                "2024-02-10",  # 农历新年
                "2024-02-12",
                "2024-02-13",
                "2024-03-29",  # 耶稣受难日
                "2024-04-04",  # 清明节
                "2024-05-01",  # 劳动节
                "2024-05-15",  # 佛诞
                "2024-06-10",  # 端午节
                "2024-07-01",  # 香港特别行政区成立纪念日
                "2024-09-18",  # 中秋节翌日
                "2024-10-01",  # 国庆日
                "2024-10-11",  # 重阳节
                "2024-12-25",  # 圣诞节
                "2024-12-26"   # 节礼日
            ],
            min_order_size=1.0,
            tick_size=0.01,
            commission_rate=0.0008,  # 0.08%
            data_providers=["futu", "tiger", "yahoo", "investing"],
            broker_apis=["futu", "tiger", "interactive_brokers"],
            supported_order_types=["market", "limit", "stop", "stop_limit"]
        )

        # 加密货币配置
        configs[MarketRegion.CRYPTO.value] = MarketConfig(
            region=MarketRegion.CRYPTO.value,
            name="加密货币市场",
            currency="USDT",
            timezone="UTC",
            trading_hours={
                "24x7": "00:00-23:59"  # 24小时交易
            },
            holidays=[],  # 无休市日
            min_order_size=0.001,
            tick_size=0.00000001,
            commission_rate=0.001,  # 0.1%
            data_providers=["binance", "coinbase", "kraken", "huobi"],
            broker_apis=["binance", "coinbase", "kraken", "okx"],
            supported_order_types=["market", "limit", "stop", "stop_limit", "oco"]
        )

        return configs

    def get_market_config(self, region: str) -> Optional[MarketConfig]:
        """获取市场配置"""
        return self.configs.get(region)

    def get_all_markets(self) -> List[str]:
        """获取所有支持的市场"""
        return list(self.configs.keys())

    def get_trading_session(self, region: str) -> TradingSession:
        """获取当前交易时段"""
        from datetime import datetime
        import pytz

        config = self.get_market_config(region)
        if not config:
            return TradingSession.CLOSED

        try:
            # 获取市场时区的当前时间
            market_tz = pytz.timezone(config.timezone)
            now = datetime.now(market_tz)
            current_time = now.strftime("%H:%M")

            # 检查是否为假日
            today = now.strftime("%Y-%m-%d")
            if today in config.holidays:
                return TradingSession.CLOSED

            # 检查是否为周末
            if now.weekday() >= 5:  # 周六、周日
                return TradingSession.CLOSED

            # 检查交易时段
            if region == MarketRegion.US.value:
                if "04:00" <= current_time < "09:30":
                    return TradingSession.PRE_MARKET
                elif "09:30" <= current_time < "16:00":
                    return TradingSession.REGULAR
                elif "16:00" <= current_time < "20:00":
                    return TradingSession.AFTER_HOURS
                else:
                    return TradingSession.CLOSED

            elif region in [MarketRegion.CN.value, MarketRegion.HK.value]:
                if ("09:30" <= current_time < "11:30" or 
                    "13:00" <= current_time < "15:00"):
                    return TradingSession.REGULAR
                else:
                    return TradingSession.CLOSED

            elif region == MarketRegion.CRYPTO.value:
                return TradingSession.REGULAR  # 24/7交易

            return TradingSession.CLOSED

        except Exception as e:
            logger.error(f"获取交易时段失败: {e}")
            return TradingSession.CLOSED

    def is_market_open(self, region: str) -> bool:
        """检查市场是否开放"""
        session = self.get_trading_session(region)
        return session in [TradingSession.PRE_MARKET, TradingSession.REGULAR, TradingSession.AFTER_HOURS]

    def get_supported_brokers(self, region: str) -> List[str]:
        """获取支持的券商列表"""
        config = self.get_market_config(region)
        return config.broker_apis if config else []

    def get_data_providers(self, region: str) -> List[str]:
        """获取数据提供商列表"""
        config = self.get_market_config(region)
        return config.data_providers if config else []

    def validate_symbol(self, symbol: str, region: str) -> bool:
        """验证股票代码格式"""
        if not symbol:
            return False

        symbol = symbol.upper()

        if region == MarketRegion.US.value:
            # 美股：1-5个字母
            return symbol.isalpha() and 1 <= len(symbol) <= 5

        elif region == MarketRegion.CN.value:
            # A股：6位数字
            return symbol.isdigit() and len(symbol) == 6

        elif region == MarketRegion.HK.value:
            # 港股：4-5位数字，可能有前缀0
            return symbol.isdigit() and 4 <= len(symbol) <= 5

        elif region == MarketRegion.CRYPTO.value:
            # 加密货币：通常以USDT、BTC等结尾
            return len(symbol) >= 6 and symbol.endswith(('USDT', 'BTC', 'ETH', 'BNB'))

        return False

    def format_symbol(self, symbol: str, region: str) -> str:
        """格式化股票代码"""
        if not symbol:
            return symbol

        symbol = symbol.upper().strip()

        if region == MarketRegion.CN.value:
            # A股：确保6位数字
            if symbol.isdigit() and len(symbol) < 6:
                symbol = symbol.zfill(6)

        elif region == MarketRegion.HK.value:
            # 港股：确保4位数字，前面补0
            if symbol.isdigit() and len(symbol) < 4:
                symbol = symbol.zfill(4)

        return symbol

    def get_market_status_info(self, region: str) -> Dict[str, Any]:
        """获取市场状态信息"""
        config = self.get_market_config(region)
        if not config:
            return {'error': '不支持的市场'}

        session = self.get_trading_session(region)
        is_open = self.is_market_open(region)

        return {
            'region': region,
            'name': config.name,
            'currency': config.currency,
            'timezone': config.timezone,
            'current_session': session.value,
            'is_open': is_open,
            'trading_hours': config.trading_hours,
            'supported_brokers': config.broker_apis,
            'data_providers': config.data_providers
        }

# 全局市场配置管理器实例
market_config_manager = MarketConfigManager()
