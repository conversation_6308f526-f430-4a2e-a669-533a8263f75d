#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据源管理服务
整合国内外主要金融数据源：akshare、东方财富、Alpha Vantage、Yahoo Finance等
"""

import logging
import asyncio
import aiohttp
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import json
import pandas as pd

from .market_config import MarketRegion, market_config_manager

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """市场数据"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float
    close: float
    timestamp: str
    market: str
    currency: str

@dataclass
class KLineData:
    """K线数据"""
    symbol: str
    timestamp: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    amount: float
    market: str

@dataclass
class CompanyInfo:
    """公司信息"""
    symbol: str
    name: str
    industry: str
    sector: str
    market_cap: float
    pe_ratio: float
    pb_ratio: float
    dividend_yield: float
    description: str
    market: str

class DataSourceManager:
    """数据源管理器"""

    def __init__(self):
        self.akshare_available = False
        self.alpha_vantage_key = None
        self.session = None
        
        # 初始化数据源
        self._initialize_data_sources()

    def _initialize_data_sources(self):
        """初始化数据源"""
        try:
            # 检查akshare可用性
            import akshare as ak
            self.akshare_available = True
            logger.info("akshare数据源初始化成功")
        except ImportError:
            logger.warning("akshare未安装，请运行: pip install akshare")
            self.akshare_available = False

        # Alpha Vantage API密钥（需要申请）
        self.alpha_vantage_key = "YOUR_API_KEY"  # 需要替换为真实API密钥

    async def get_realtime_data(self, symbols: List[str], market: str) -> List[MarketData]:
        """获取实时行情数据"""
        try:
            if market == MarketRegion.CN.value:
                return await self._get_cn_realtime_data(symbols)
            elif market == MarketRegion.US.value:
                return await self._get_us_realtime_data(symbols)
            elif market == MarketRegion.HK.value:
                return await self._get_hk_realtime_data(symbols)
            elif market == MarketRegion.CRYPTO.value:
                return await self._get_crypto_realtime_data(symbols)
            else:
                logger.error(f"不支持的市场: {market}")
                return []

        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return []

    async def _get_cn_realtime_data(self, symbols: List[str]) -> List[MarketData]:
        """获取A股实时数据"""
        market_data_list = []
        
        if not self.akshare_available:
            # 返回模拟数据
            return self._generate_mock_cn_data(symbols)

        try:
            import akshare as ak
            
            for symbol in symbols:
                try:
                    # 获取实时行情
                    df = ak.stock_zh_a_spot_em()
                    stock_data = df[df['代码'] == symbol]
                    
                    if not stock_data.empty:
                        row = stock_data.iloc[0]
                        
                        market_data = MarketData(
                            symbol=symbol,
                            price=float(row['最新价']),
                            change=float(row['涨跌额']),
                            change_percent=float(row['涨跌幅']),
                            volume=int(row['成交量']),
                            high=float(row['最高']),
                            low=float(row['最低']),
                            open=float(row['今开']),
                            close=float(row['昨收']),
                            timestamp=datetime.now().isoformat(),
                            market=MarketRegion.CN.value,
                            currency="CNY"
                        )
                        market_data_list.append(market_data)
                    else:
                        logger.warning(f"未找到股票数据: {symbol}")

                except Exception as e:
                    logger.error(f"获取{symbol}数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"akshare获取A股数据失败: {e}")
            return self._generate_mock_cn_data(symbols)

        return market_data_list

    async def _get_us_realtime_data(self, symbols: List[str]) -> List[MarketData]:
        """获取美股实时数据"""
        market_data_list = []

        try:
            # 使用Yahoo Finance API
            for symbol in symbols:
                try:
                    url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if 'chart' in data and data['chart']['result']:
                                    result = data['chart']['result'][0]
                                    meta = result['meta']
                                    
                                    market_data = MarketData(
                                        symbol=symbol,
                                        price=float(meta.get('regularMarketPrice', 0)),
                                        change=float(meta.get('regularMarketPrice', 0) - meta.get('previousClose', 0)),
                                        change_percent=float((meta.get('regularMarketPrice', 0) - meta.get('previousClose', 0)) / meta.get('previousClose', 1) * 100),
                                        volume=int(meta.get('regularMarketVolume', 0)),
                                        high=float(meta.get('regularMarketDayHigh', 0)),
                                        low=float(meta.get('regularMarketDayLow', 0)),
                                        open=float(meta.get('regularMarketOpen', 0)),
                                        close=float(meta.get('previousClose', 0)),
                                        timestamp=datetime.now().isoformat(),
                                        market=MarketRegion.US.value,
                                        currency="USD"
                                    )
                                    market_data_list.append(market_data)

                except Exception as e:
                    logger.error(f"获取{symbol}美股数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"获取美股数据失败: {e}")
            return self._generate_mock_us_data(symbols)

        return market_data_list

    async def _get_hk_realtime_data(self, symbols: List[str]) -> List[MarketData]:
        """获取港股实时数据"""
        market_data_list = []

        if not self.akshare_available:
            return self._generate_mock_hk_data(symbols)

        try:
            import akshare as ak
            
            for symbol in symbols:
                try:
                    # 港股代码格式化
                    hk_symbol = symbol.zfill(5) if len(symbol) < 5 else symbol
                    
                    # 获取港股实时数据
                    df = ak.stock_hk_spot_em()
                    stock_data = df[df['代码'] == hk_symbol]
                    
                    if not stock_data.empty:
                        row = stock_data.iloc[0]
                        
                        market_data = MarketData(
                            symbol=symbol,
                            price=float(row['最新价']),
                            change=float(row['涨跌额']),
                            change_percent=float(row['涨跌幅']),
                            volume=int(row['成交量']),
                            high=float(row['最高']),
                            low=float(row['最低']),
                            open=float(row['今开']),
                            close=float(row['昨收']),
                            timestamp=datetime.now().isoformat(),
                            market=MarketRegion.HK.value,
                            currency="HKD"
                        )
                        market_data_list.append(market_data)

                except Exception as e:
                    logger.error(f"获取{symbol}港股数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"获取港股数据失败: {e}")
            return self._generate_mock_hk_data(symbols)

        return market_data_list

    async def _get_crypto_realtime_data(self, symbols: List[str]) -> List[MarketData]:
        """获取加密货币实时数据"""
        market_data_list = []

        try:
            # 使用币安API
            for symbol in symbols:
                try:
                    url = f"https://api.binance.com/api/v3/ticker/24hr?symbol={symbol}"
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                market_data = MarketData(
                                    symbol=symbol,
                                    price=float(data['lastPrice']),
                                    change=float(data['priceChange']),
                                    change_percent=float(data['priceChangePercent']),
                                    volume=int(float(data['volume'])),
                                    high=float(data['highPrice']),
                                    low=float(data['lowPrice']),
                                    open=float(data['openPrice']),
                                    close=float(data['prevClosePrice']),
                                    timestamp=datetime.now().isoformat(),
                                    market=MarketRegion.CRYPTO.value,
                                    currency="USDT"
                                )
                                market_data_list.append(market_data)

                except Exception as e:
                    logger.error(f"获取{symbol}加密货币数据失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"获取加密货币数据失败: {e}")
            return self._generate_mock_crypto_data(symbols)

        return market_data_list

    async def get_kline_data(self, symbol: str, market: str, period: str = "1d", limit: int = 100) -> List[KLineData]:
        """获取K线数据"""
        try:
            if market == MarketRegion.CN.value:
                return await self._get_cn_kline_data(symbol, period, limit)
            elif market == MarketRegion.US.value:
                return await self._get_us_kline_data(symbol, period, limit)
            elif market == MarketRegion.HK.value:
                return await self._get_hk_kline_data(symbol, period, limit)
            elif market == MarketRegion.CRYPTO.value:
                return await self._get_crypto_kline_data(symbol, period, limit)
            else:
                logger.error(f"不支持的市场: {market}")
                return []

        except Exception as e:
            logger.error(f"获取K线数据失败: {e}")
            return []

    async def _get_cn_kline_data(self, symbol: str, period: str, limit: int) -> List[KLineData]:
        """获取A股K线数据"""
        if not self.akshare_available:
            return []

        try:
            import akshare as ak
            
            # 转换周期格式
            period_map = {
                "1m": "1",
                "5m": "5", 
                "15m": "15",
                "30m": "30",
                "1h": "60",
                "1d": "daily"
            }
            
            ak_period = period_map.get(period, "daily")
            
            if ak_period == "daily":
                df = ak.stock_zh_a_hist(symbol=symbol, period="daily", adjust="qfq")
            else:
                df = ak.stock_zh_a_hist_min_em(symbol=symbol, period=ak_period)
            
            if df.empty:
                return []
            
            # 限制数据量
            df = df.tail(limit)
            
            kline_data = []
            for _, row in df.iterrows():
                kline = KLineData(
                    symbol=symbol,
                    timestamp=row.name.strftime('%Y-%m-%d %H:%M:%S') if hasattr(row.name, 'strftime') else str(row.name),
                    open=float(row['开盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    close=float(row['收盘']),
                    volume=int(row['成交量']),
                    amount=float(row.get('成交额', 0)),
                    market=MarketRegion.CN.value
                )
                kline_data.append(kline)
            
            return kline_data

        except Exception as e:
            logger.error(f"获取A股K线数据失败: {e}")
            return []

    async def get_company_info(self, symbol: str, market: str) -> Optional[CompanyInfo]:
        """获取公司基本信息"""
        try:
            if market == MarketRegion.CN.value:
                return await self._get_cn_company_info(symbol)
            elif market == MarketRegion.US.value:
                return await self._get_us_company_info(symbol)
            elif market == MarketRegion.HK.value:
                return await self._get_hk_company_info(symbol)
            else:
                return None

        except Exception as e:
            logger.error(f"获取公司信息失败: {e}")
            return None

    async def _get_cn_company_info(self, symbol: str) -> Optional[CompanyInfo]:
        """获取A股公司信息"""
        if not self.akshare_available:
            return None

        try:
            import akshare as ak
            
            # 获取股票基本信息
            df = ak.stock_individual_info_em(symbol=symbol)
            
            if df.empty:
                return None
            
            info_dict = dict(zip(df['item'], df['value']))
            
            company_info = CompanyInfo(
                symbol=symbol,
                name=info_dict.get('股票简称', ''),
                industry=info_dict.get('行业', ''),
                sector=info_dict.get('所属概念', ''),
                market_cap=float(info_dict.get('总市值', 0)),
                pe_ratio=float(info_dict.get('市盈率-动态', 0)),
                pb_ratio=float(info_dict.get('市净率', 0)),
                dividend_yield=float(info_dict.get('股息率', 0)),
                description=info_dict.get('主营业务', ''),
                market=MarketRegion.CN.value
            )
            
            return company_info

        except Exception as e:
            logger.error(f"获取A股公司信息失败: {e}")
            return None

    def _generate_mock_cn_data(self, symbols: List[str]) -> List[MarketData]:
        """生成A股模拟数据"""
        import random
        
        mock_data = []
        for symbol in symbols:
            base_price = random.uniform(10, 50)
            change = random.uniform(-2, 2)
            
            market_data = MarketData(
                symbol=symbol,
                price=base_price,
                change=change,
                change_percent=(change / base_price) * 100,
                volume=random.randint(100000, 5000000),
                high=base_price + random.uniform(0, 2),
                low=base_price - random.uniform(0, 2),
                open=base_price + random.uniform(-1, 1),
                close=base_price - change,
                timestamp=datetime.now().isoformat(),
                market=MarketRegion.CN.value,
                currency="CNY"
            )
            mock_data.append(market_data)
        
        return mock_data

    def _generate_mock_us_data(self, symbols: List[str]) -> List[MarketData]:
        """生成美股模拟数据"""
        import random
        
        mock_data = []
        for symbol in symbols:
            base_price = random.uniform(50, 300)
            change = random.uniform(-10, 10)
            
            market_data = MarketData(
                symbol=symbol,
                price=base_price,
                change=change,
                change_percent=(change / base_price) * 100,
                volume=random.randint(1000000, 50000000),
                high=base_price + random.uniform(0, 10),
                low=base_price - random.uniform(0, 10),
                open=base_price + random.uniform(-5, 5),
                close=base_price - change,
                timestamp=datetime.now().isoformat(),
                market=MarketRegion.US.value,
                currency="USD"
            )
            mock_data.append(market_data)
        
        return mock_data

    def _generate_mock_hk_data(self, symbols: List[str]) -> List[MarketData]:
        """生成港股模拟数据"""
        import random
        
        mock_data = []
        for symbol in symbols:
            base_price = random.uniform(20, 200)
            change = random.uniform(-5, 5)
            
            market_data = MarketData(
                symbol=symbol,
                price=base_price,
                change=change,
                change_percent=(change / base_price) * 100,
                volume=random.randint(500000, 10000000),
                high=base_price + random.uniform(0, 5),
                low=base_price - random.uniform(0, 5),
                open=base_price + random.uniform(-2, 2),
                close=base_price - change,
                timestamp=datetime.now().isoformat(),
                market=MarketRegion.HK.value,
                currency="HKD"
            )
            mock_data.append(market_data)
        
        return mock_data

    def _generate_mock_crypto_data(self, symbols: List[str]) -> List[MarketData]:
        """生成加密货币模拟数据"""
        import random
        
        mock_data = []
        for symbol in symbols:
            if 'BTC' in symbol:
                base_price = random.uniform(40000, 60000)
            elif 'ETH' in symbol:
                base_price = random.uniform(2000, 4000)
            else:
                base_price = random.uniform(0.1, 100)
            
            change = random.uniform(-1000, 1000)
            
            market_data = MarketData(
                symbol=symbol,
                price=base_price,
                change=change,
                change_percent=(change / base_price) * 100,
                volume=random.randint(10000, 1000000),
                high=base_price + random.uniform(0, 2000),
                low=base_price - random.uniform(0, 2000),
                open=base_price + random.uniform(-500, 500),
                close=base_price - change,
                timestamp=datetime.now().isoformat(),
                market=MarketRegion.CRYPTO.value,
                currency="USDT"
            )
            mock_data.append(market_data)
        
        return mock_data

    async def get_market_overview(self, market: str) -> Dict[str, Any]:
        """获取市场概览"""
        try:
            if market == MarketRegion.CN.value:
                return await self._get_cn_market_overview()
            elif market == MarketRegion.US.value:
                return await self._get_us_market_overview()
            else:
                return {}

        except Exception as e:
            logger.error(f"获取市场概览失败: {e}")
            return {}

    async def _get_cn_market_overview(self) -> Dict[str, Any]:
        """获取A股市场概览"""
        if not self.akshare_available:
            return {
                'shanghai_index': {'value': 3200.0, 'change': 1.2, 'change_percent': 0.04},
                'shenzhen_index': {'value': 12000.0, 'change': -15.0, 'change_percent': -0.12},
                'chinext_index': {'value': 2800.0, 'change': 8.5, 'change_percent': 0.30}
            }

        try:
            import akshare as ak
            
            # 获取主要指数
            overview = {}
            
            # 上证指数
            sh_index = ak.stock_zh_index_spot_em(symbol="sh000001")
            if not sh_index.empty:
                row = sh_index.iloc[0]
                overview['shanghai_index'] = {
                    'value': float(row['最新价']),
                    'change': float(row['涨跌额']),
                    'change_percent': float(row['涨跌幅'])
                }
            
            # 深证成指
            sz_index = ak.stock_zh_index_spot_em(symbol="sz399001")
            if not sz_index.empty:
                row = sz_index.iloc[0]
                overview['shenzhen_index'] = {
                    'value': float(row['最新价']),
                    'change': float(row['涨跌额']),
                    'change_percent': float(row['涨跌幅'])
                }
            
            return overview

        except Exception as e:
            logger.error(f"获取A股市场概览失败: {e}")
            return {}

    async def _get_us_market_overview(self) -> Dict[str, Any]:
        """获取美股市场概览"""
        try:
            # 获取主要指数：道琼斯、纳斯达克、标普500
            indices = ['^DJI', '^IXIC', '^GSPC']
            overview = {}
            
            for index in indices:
                try:
                    url = f"https://query1.finance.yahoo.com/v8/finance/chart/{index}"
                    
                    async with aiohttp.ClientSession() as session:
                        async with session.get(url) as response:
                            if response.status == 200:
                                data = await response.json()
                                
                                if 'chart' in data and data['chart']['result']:
                                    result = data['chart']['result'][0]
                                    meta = result['meta']
                                    
                                    current_price = float(meta.get('regularMarketPrice', 0))
                                    previous_close = float(meta.get('previousClose', 0))
                                    change = current_price - previous_close
                                    change_percent = (change / previous_close) * 100 if previous_close > 0 else 0
                                    
                                    index_name = {
                                        '^DJI': 'dow_jones',
                                        '^IXIC': 'nasdaq',
                                        '^GSPC': 'sp500'
                                    }.get(index, index)
                                    
                                    overview[index_name] = {
                                        'value': current_price,
                                        'change': change,
                                        'change_percent': change_percent
                                    }

                except Exception as e:
                    logger.error(f"获取{index}数据失败: {e}")
                    continue
            
            return overview

        except Exception as e:
            logger.error(f"获取美股市场概览失败: {e}")
            return {}

# 全局数据源管理器实例
data_source_manager = DataSourceManager()
