// 实盘交易页面JavaScript

class LiveTradingManager {
    constructor() {
        this.currentAccount = 'MOCK_001';
        this.refreshInterval = 5000; // 5秒刷新
        this.refreshTimer = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // 订单类型变化
        document.getElementById('orderType').addEventListener('change', (e) => {
            this.togglePriceFields(e.target.value);
        });

        // 订单表单提交
        document.getElementById('orderForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitOrder();
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            this.stopAutoRefresh();
        });
    }

    togglePriceFields(orderType) {
        const priceField = document.getElementById('priceField');
        const stopPriceField = document.getElementById('stopPriceField');

        // 重置显示
        priceField.style.display = 'none';
        stopPriceField.style.display = 'none';

        // 根据订单类型显示相应字段
        if (orderType === 'limit' || orderType === 'stop_limit') {
            priceField.style.display = 'block';
        }
        if (orderType === 'stop' || orderType === 'stop_limit') {
            stopPriceField.style.display = 'block';
        }
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadAccountSummary(),
                this.loadPositions(),
                this.loadOrders(),
                this.loadPerformanceMetrics(),
                this.loadRiskAlerts()
            ]);
        } catch (error) {
            console.error('加载初始数据失败:', error);
            this.showError('加载数据失败，请刷新页面重试');
        }
    }

    async loadAccountSummary() {
        try {
            const response = await fetch(`/api/trading/portfolio/${this.currentAccount}`);
            const data = await response.json();

            if (data.success && data.summary) {
                this.updateAccountSummary(data.summary);
            }
        } catch (error) {
            console.error('加载账户摘要失败:', error);
        }
    }

    updateAccountSummary(summary) {
        document.getElementById('totalValue').textContent = this.formatCurrency(summary.total_value);
        document.getElementById('cashBalance').textContent = this.formatCurrency(summary.cash_balance);
        document.getElementById('positionsValue').textContent = this.formatCurrency(summary.positions_value);
        
        const unrealizedPnl = document.getElementById('unrealizedPnl');
        unrealizedPnl.textContent = this.formatCurrency(summary.unrealized_pnl);
        unrealizedPnl.className = `metric-value ${summary.unrealized_pnl >= 0 ? 'price-up' : 'price-down'}`;
        
        const dayPnl = document.getElementById('dayPnl');
        dayPnl.textContent = this.formatCurrency(summary.day_pnl);
        dayPnl.className = `metric-value ${summary.day_pnl >= 0 ? 'price-up' : 'price-down'}`;
        
        const totalReturn = document.getElementById('totalReturn');
        totalReturn.textContent = this.formatPercent(summary.total_return_percent);
        totalReturn.className = `metric-value ${summary.total_return_percent >= 0 ? 'price-up' : 'price-down'}`;
    }

    async loadPositions() {
        try {
            const response = await fetch(`/api/trading/positions/${this.currentAccount}`);
            const data = await response.json();

            if (data.success) {
                this.updatePositionsTable(data.positions);
            }
        } catch (error) {
            console.error('加载持仓失败:', error);
        }
    }

    updatePositionsTable(positions) {
        const tbody = document.getElementById('positionsTable');
        
        if (!positions || positions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无持仓</td></tr>';
            return;
        }

        tbody.innerHTML = positions.map(position => `
            <tr>
                <td>${position.symbol}</td>
                <td>${position.quantity}</td>
                <td>$${position.avg_cost.toFixed(2)}</td>
                <td class="real-time-price">$${position.market_price.toFixed(2)}</td>
                <td>$${position.market_value.toFixed(2)}</td>
                <td class="${position.unrealized_pnl >= 0 ? 'price-up' : 'price-down'}">
                    $${position.unrealized_pnl.toFixed(2)}
                </td>
                <td class="${position.unrealized_pnl_percent >= 0 ? 'price-up' : 'price-down'}">
                    ${position.unrealized_pnl_percent.toFixed(2)}%
                </td>
            </tr>
        `).join('');
    }

    async loadOrders() {
        try {
            const response = await fetch(`/api/trading/orders?account_id=${this.currentAccount}&limit=20`);
            const data = await response.json();

            if (data.success) {
                this.updateOrdersTable(data.orders);
            }
        } catch (error) {
            console.error('加载订单失败:', error);
        }
    }

    updateOrdersTable(orders) {
        const tbody = document.getElementById('ordersTable');
        
        if (!orders || orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无订单</td></tr>';
            return;
        }

        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>${order.order_id.substring(0, 8)}...</td>
                <td>${order.symbol}</td>
                <td>
                    <span class="badge ${order.side === 'buy' ? 'bg-success' : 'bg-danger'}">
                        ${order.side === 'buy' ? '买入' : '卖出'}
                    </span>
                </td>
                <td>${this.getOrderTypeText(order.order_type)}</td>
                <td>${order.quantity}</td>
                <td>${order.price ? '$' + order.price.toFixed(2) : '市价'}</td>
                <td>
                    <span class="order-status status-${order.status}">
                        ${this.getOrderStatusText(order.status)}
                    </span>
                </td>
                <td>${new Date(order.created_at).toLocaleString()}</td>
                <td>
                    ${order.status === 'submitted' || order.status === 'partially_filled' ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="liveTradingManager.cancelOrder('${order.order_id}')">
                            <i class="fas fa-times"></i>
                        </button>` : 
                        '-'
                    }
                </td>
            </tr>
        `).join('');
    }

    async loadPerformanceMetrics() {
        try {
            const response = await fetch(`/api/trading/performance/${this.currentAccount}`);
            const data = await response.json();

            if (data.success && data.metrics) {
                this.updatePerformanceMetrics(data.metrics);
            }
        } catch (error) {
            console.error('加载绩效指标失败:', error);
        }
    }

    updatePerformanceMetrics(metrics) {
        document.getElementById('totalReturnPercent').textContent = this.formatPercent(metrics.total_return_percent);
        document.getElementById('annualizedReturn').textContent = this.formatPercent(metrics.annualized_return_percent);
        document.getElementById('sharpeRatio').textContent = metrics.sharpe_ratio.toFixed(2);
        document.getElementById('maxDrawdown').textContent = this.formatPercent(metrics.max_drawdown_percent);
        document.getElementById('winRate').textContent = this.formatPercent(metrics.win_rate);
        document.getElementById('totalTrades').textContent = metrics.total_trades;
    }

    async loadRiskAlerts() {
        try {
            const response = await fetch(`/api/trading/alerts?account_id=${this.currentAccount}`);
            const data = await response.json();

            if (data.success) {
                this.updateRiskAlerts(data.alerts);
            }
        } catch (error) {
            console.error('加载风险预警失败:', error);
        }
    }

    updateRiskAlerts(alerts) {
        const alertsList = document.getElementById('alertsList');
        
        if (!alerts || alerts.length === 0) {
            alertsList.innerHTML = '<p class="text-muted">暂无风险预警</p>';
            return;
        }

        alertsList.innerHTML = alerts.map(alert => `
            <div class="risk-alert p-2 mb-2 rounded">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${this.getAlertTypeText(alert.alert_type)}</strong>
                        <p class="mb-1">${alert.message}</p>
                        <small>${new Date(alert.created_at).toLocaleString()}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="liveTradingManager.resolveAlert('${alert.alert_id}')">
                        <i class="fas fa-check"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    async submitOrder() {
        const formData = {
            symbol: document.getElementById('symbol').value.toUpperCase(),
            side: document.getElementById('side').value,
            order_type: document.getElementById('orderType').value,
            quantity: parseFloat(document.getElementById('quantity').value),
            time_in_force: document.getElementById('timeInForce').value,
            account_id: this.currentAccount
        };

        // 添加价格字段（如果需要）
        const orderType = formData.order_type;
        if (orderType === 'limit' || orderType === 'stop_limit') {
            const price = document.getElementById('price').value;
            if (!price) {
                this.showError('请输入价格');
                return;
            }
            formData.price = parseFloat(price);
        }

        if (orderType === 'stop' || orderType === 'stop_limit') {
            const stopPrice = document.getElementById('stopPrice').value;
            if (!stopPrice) {
                this.showError('请输入止损价格');
                return;
            }
            formData.stop_price = parseFloat(stopPrice);
        }

        try {
            const response = await fetch('/api/trading/orders', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('订单提交成功');
                document.getElementById('orderForm').reset();
                this.togglePriceFields('market'); // 重置价格字段显示
                
                // 刷新数据
                await this.loadOrders();
                await this.loadAccountSummary();
            } else {
                this.showError(data.error || '订单提交失败');
            }
        } catch (error) {
            console.error('提交订单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async cancelOrder(orderId) {
        if (!confirm('确定要取消这个订单吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/trading/orders/${orderId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('订单取消成功');
                await this.loadOrders();
            } else {
                this.showError(data.error || '订单取消失败');
            }
        } catch (error) {
            console.error('取消订单失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    async resolveAlert(alertId) {
        try {
            const response = await fetch(`/api/trading/alerts/${alertId}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('预警已解决');
                await this.loadRiskAlerts();
            } else {
                this.showError(data.error || '解决预警失败');
            }
        } catch (error) {
            console.error('解决预警失败:', error);
            this.showError('网络错误，请重试');
        }
    }

    startAutoRefresh() {
        this.refreshTimer = setInterval(() => {
            this.loadAccountSummary();
            this.loadPositions();
            this.loadOrders();
            this.loadRiskAlerts();
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    // 工具方法
    formatCurrency(value) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(value);
    }

    formatPercent(value) {
        return `${value.toFixed(2)}%`;
    }

    getOrderTypeText(type) {
        const types = {
            'market': '市价单',
            'limit': '限价单',
            'stop': '止损单',
            'stop_limit': '止损限价单'
        };
        return types[type] || type;
    }

    getOrderStatusText(status) {
        const statuses = {
            'pending': '待提交',
            'submitted': '已提交',
            'filled': '已成交',
            'partially_filled': '部分成交',
            'cancelled': '已取消',
            'rejected': '已拒绝',
            'expired': '已过期'
        };
        return statuses[status] || status;
    }

    getAlertTypeText(type) {
        const types = {
            'position_limit': '持仓限制',
            'drawdown': '回撤预警',
            'concentration': '集中度风险',
            'leverage': '杠杆风险',
            'daily_loss': '日内亏损',
            'volatility': '波动率预警'
        };
        return types[type] || type;
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'danger');
    }

    showToast(message, type = 'info') {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
}

// 初始化实盘交易管理器
const liveTradingManager = new LiveTradingManager();
