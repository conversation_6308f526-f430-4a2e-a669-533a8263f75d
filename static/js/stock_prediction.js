// 股票预测页面JavaScript

class StockPredictionManager {
    constructor() {
        this.currentMarket = 'us';
        this.selectedAnalysts = [];
        this.supportedAnalysts = {};
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSupportedAnalysts();
        this.updateAnalystChips();
    }

    setupEventListeners() {
        // 市场选择
        document.querySelectorAll('.market-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchMarket(e.target.dataset.market);
            });
        });

        // 分析师选择
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('analyst-chip')) {
                this.toggleAnalyst(e.target.dataset.analyst);
            }
        });
    }

    async loadSupportedAnalysts() {
        try {
            const response = await fetch('/api/stock-prediction/analysts');
            const data = await response.json();
            
            if (data.success) {
                this.supportedAnalysts = data.analysts;
            }
        } catch (error) {
            console.error('加载分析师列表失败:', error);
            // 使用默认分析师列表
            this.supportedAnalysts = {
                'us': ['warren_buffett', 'ben_graham', 'peter_lynch', 'cathie_wood', 'bill_ackman'],
                'cn': ['ben_graham', 'peter_lynch', 'warren_buffett'],
                'hk': ['warren_buffett', 'ben_graham', 'peter_lynch'],
                'crypto': ['cathie_wood', 'stanley_druckenmiller', 'michael_burry']
            };
        }
    }

    switchMarket(market) {
        // 更新UI
        document.querySelectorAll('.market-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-market="${market}"]`).classList.add('active');
        
        this.currentMarket = market;
        this.selectedAnalysts = [];
        this.updateAnalystChips();
        this.updatePlaceholder();
    }

    updateAnalystChips() {
        const container = document.getElementById('analystChips');
        const analysts = this.supportedAnalysts[this.currentMarket] || [];
        
        container.innerHTML = analysts.map(analyst => {
            const displayName = this.getAnalystDisplayName(analyst);
            return `
                <span class="analyst-chip" data-analyst="${analyst}">
                    <i class="fas fa-user-tie"></i> ${displayName}
                </span>
            `;
        }).join('');
    }

    updatePlaceholder() {
        const input = document.getElementById('symbolInput');
        const placeholders = {
            'us': '输入美股代码，如: AAPL,TSLA,GOOGL',
            'cn': '输入A股代码，如: 000001,000002,600036',
            'hk': '输入港股代码，如: 0700,0941,1810',
            'crypto': '输入加密货币，如: BTCUSDT,ETHUSDT,BNBUSDT'
        };
        
        input.placeholder = placeholders[this.currentMarket];
        input.value = '';
    }

    toggleAnalyst(analyst) {
        const chip = document.querySelector(`[data-analyst="${analyst}"]`);
        
        if (this.selectedAnalysts.includes(analyst)) {
            // 取消选择
            this.selectedAnalysts = this.selectedAnalysts.filter(a => a !== analyst);
            chip.classList.remove('selected');
        } else {
            // 选择
            this.selectedAnalysts.push(analyst);
            chip.classList.add('selected');
        }
    }

    getAnalystDisplayName(analyst) {
        const names = {
            'warren_buffett': '沃伦·巴菲特',
            'ben_graham': '本杰明·格雷厄姆',
            'peter_lynch': '彼得·林奇',
            'cathie_wood': '凯茜·伍德',
            'bill_ackman': '比尔·阿克曼',
            'charlie_munger': '查理·芒格',
            'michael_burry': '迈克尔·伯里',
            'aswath_damodaran': '阿斯沃斯·达摩达兰',
            'phil_fisher': '菲利普·费雪',
            'stanley_druckenmiller': '斯坦利·德鲁肯米勒'
        };
        
        return names[analyst] || analyst;
    }

    async startPrediction() {
        const symbols = document.getElementById('symbolInput').value.trim();
        const model = document.getElementById('modelSelect').value;
        
        if (!symbols) {
            this.showAlert('请输入股票代码', 'warning');
            return;
        }

        if (this.selectedAnalysts.length === 0) {
            this.showAlert('请至少选择一位分析师', 'warning');
            return;
        }

        // 显示加载状态
        this.showLoading(true);
        this.hideResults();

        try {
            const response = await fetch('/api/stock-prediction/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    symbols: symbols.split(',').map(s => s.trim().toUpperCase()),
                    analysts: this.selectedAnalysts,
                    market_region: this.currentMarket,
                    model_name: model,
                    include_reasoning: true
                })
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data);
            } else {
                this.showAlert(data.error || '预测失败', 'danger');
            }

        } catch (error) {
            console.error('预测请求失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }

    hideResults() {
        document.getElementById('consensusResults').style.display = 'none';
        document.getElementById('predictionResults').style.display = 'none';
    }

    displayResults(data) {
        this.displayConsensus(data.consensus);
        this.displayPredictions(data.predictions);
        
        // 显示结果
        document.getElementById('consensusResults').style.display = 'block';
        document.getElementById('predictionResults').style.display = 'block';
    }

    displayConsensus(consensus) {
        const container = document.getElementById('consensusResults');
        
        container.innerHTML = consensus.map(cons => `
            <div class="consensus-section">
                <h4><i class="fas fa-chart-line"></i> ${cons.symbol} - 共识预测</h4>
                
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="signal-badge signal-${cons.consensus_signal} me-3">
                                ${this.getSignalText(cons.consensus_signal)}
                            </span>
                            <div>
                                <div>置信度: ${cons.consensus_confidence.toFixed(1)}%</div>
                                <small>基于 ${cons.analyst_count} 位分析师意见</small>
                            </div>
                        </div>
                        
                        ${cons.avg_target_price ? `
                            <div class="mb-2">
                                <strong>平均目标价: $${cons.avg_target_price.toFixed(2)}</strong>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="col-md-6">
                        <div class="consensus-stats">
                            <div class="stat-item">
                                <div class="stat-value text-success">${cons.bullish_count}</div>
                                <div class="stat-label">看涨</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-danger">${cons.bearish_count}</div>
                                <div class="stat-label">看跌</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value text-warning">${cons.neutral_count}</div>
                                <div class="stat-label">中性</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    displayPredictions(predictions) {
        const container = document.getElementById('predictionResults');
        
        // 按股票分组
        const groupedPredictions = {};
        predictions.forEach(pred => {
            if (!groupedPredictions[pred.symbol]) {
                groupedPredictions[pred.symbol] = [];
            }
            groupedPredictions[pred.symbol].push(pred);
        });

        container.innerHTML = Object.entries(groupedPredictions).map(([symbol, preds]) => `
            <div class="prediction-card">
                <h4><i class="fas fa-chart-bar"></i> ${symbol} - 详细分析</h4>
                
                <div class="row">
                    ${preds.map(pred => `
                        <div class="col-md-6 mb-3">
                            <div class="analyst-card ${pred.signal}">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-user-tie"></i> 
                                            ${this.getAnalystDisplayName(pred.analyst_name)}
                                        </h6>
                                        <span class="signal-badge signal-${pred.signal}">
                                            ${this.getSignalText(pred.signal)}
                                        </span>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-bold">${pred.confidence.toFixed(1)}%</div>
                                        <small class="text-muted">置信度</small>
                                    </div>
                                </div>
                                
                                <div class="confidence-bar">
                                    <div class="confidence-fill" style="width: ${pred.confidence}%"></div>
                                </div>
                                
                                ${pred.target_price ? `
                                    <div class="mt-2">
                                        <small><strong>目标价:</strong> $${pred.target_price.toFixed(2)}</small>
                                    </div>
                                ` : ''}
                                
                                ${pred.reasoning ? `
                                    <div class="reasoning-text">
                                        <small><strong>分析理由:</strong></small><br>
                                        <small>${pred.reasoning}</small>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `).join('');
    }

    getSignalText(signal) {
        const texts = {
            'bullish': '看涨',
            'bearish': '看跌',
            'neutral': '中性'
        };
        return texts[signal] || signal;
    }

    showAlert(message, type = 'info') {
        // 创建alert元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.position = 'fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.style.minWidth = '300px';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 全局函数
function startPrediction() {
    stockPredictionManager.startPrediction();
}

// 初始化股票预测管理器
const stockPredictionManager = new StockPredictionManager();
