// 实时行情数据页面JavaScript

class MarketDataManager {
    constructor() {
        this.currentMarket = 'cn';
        this.autoRefresh = false;
        this.refreshInterval = 10000; // 10秒
        this.refreshTimer = null;
        this.watchedSymbols = [];
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadMarketOverview();
        this.updatePlaceholder();
    }

    setupEventListeners() {
        // 回车键搜索
        document.getElementById('symbolInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchStocks();
            }
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else if (this.autoRefresh) {
                this.startAutoRefresh();
            }
        });
    }

    switchMarket(market) {
        // 更新UI
        document.querySelectorAll('.market-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-market="${market}"]`).classList.add('active');
        
        this.currentMarket = market;
        this.updatePlaceholder();
        this.loadMarketOverview();
        
        // 清空当前数据
        document.getElementById('stockDataContainer').style.display = 'none';
        this.watchedSymbols = [];
    }

    updatePlaceholder() {
        const input = document.getElementById('symbolInput');
        const placeholders = {
            'cn': '输入A股代码，如: 000001,000002,600036',
            'us': '输入美股代码，如: AAPL,TSLA,GOOGL',
            'hk': '输入港股代码，如: 0700,0941,1810',
            'crypto': '输入加密货币，如: BTCUSDT,ETHUSDT,BNBUSDT'
        };
        
        input.placeholder = placeholders[this.currentMarket];
        input.value = '';
    }

    async loadMarketOverview() {
        try {
            this.showRefreshIndicator(true);
            
            const response = await fetch(`/api/market-data/overview/${this.currentMarket}`);
            const data = await response.json();

            if (data.success) {
                this.updateMarketOverview(data.data);
            } else {
                console.error('获取市场概览失败:', data.error);
            }

        } catch (error) {
            console.error('获取市场概览失败:', error);
        } finally {
            this.showRefreshIndicator(false);
        }
    }

    updateMarketOverview(overviewData) {
        const container = document.getElementById('indexList');
        
        if (!overviewData || Object.keys(overviewData).length === 0) {
            container.innerHTML = '<div class="col-12 text-center">暂无市场数据</div>';
            return;
        }

        const indexItems = [];
        
        if (this.currentMarket === 'cn') {
            // A股指数
            if (overviewData.shanghai_index) {
                indexItems.push(this.createIndexItem(
                    '上证指数',
                    overviewData.shanghai_index.value,
                    overviewData.shanghai_index.change,
                    overviewData.shanghai_index.change_percent
                ));
            }
            
            if (overviewData.shenzhen_index) {
                indexItems.push(this.createIndexItem(
                    '深证成指',
                    overviewData.shenzhen_index.value,
                    overviewData.shenzhen_index.change,
                    overviewData.shenzhen_index.change_percent
                ));
            }
            
            if (overviewData.chinext_index) {
                indexItems.push(this.createIndexItem(
                    '创业板指',
                    overviewData.chinext_index.value,
                    overviewData.chinext_index.change,
                    overviewData.chinext_index.change_percent
                ));
            }
        } else if (this.currentMarket === 'us') {
            // 美股指数
            if (overviewData.dow_jones) {
                indexItems.push(this.createIndexItem(
                    '道琼斯',
                    overviewData.dow_jones.value,
                    overviewData.dow_jones.change,
                    overviewData.dow_jones.change_percent
                ));
            }
            
            if (overviewData.nasdaq) {
                indexItems.push(this.createIndexItem(
                    '纳斯达克',
                    overviewData.nasdaq.value,
                    overviewData.nasdaq.change,
                    overviewData.nasdaq.change_percent
                ));
            }
            
            if (overviewData.sp500) {
                indexItems.push(this.createIndexItem(
                    '标普500',
                    overviewData.sp500.value,
                    overviewData.sp500.change,
                    overviewData.sp500.change_percent
                ));
            }
        }

        if (indexItems.length === 0) {
            container.innerHTML = '<div class="col-12 text-center">暂无指数数据</div>';
        } else {
            container.innerHTML = indexItems.join('');
        }
    }

    createIndexItem(name, value, change, changePercent) {
        const changeClass = change >= 0 ? 'price-up' : 'price-down';
        const changeSign = change >= 0 ? '+' : '';
        
        return `
            <div class="col-md-4">
                <div class="index-item">
                    <div class="index-value">${this.formatNumber(value)}</div>
                    <div class="index-change ${changeClass}">
                        ${changeSign}${this.formatNumber(change)} (${changeSign}${changePercent.toFixed(2)}%)
                    </div>
                    <div class="index-name">${name}</div>
                </div>
            </div>
        `;
    }

    async searchStocks() {
        const input = document.getElementById('symbolInput');
        const symbols = input.value.trim();
        
        if (!symbols) {
            this.showAlert('请输入股票代码', 'warning');
            return;
        }

        const symbolList = symbols.split(',').map(s => s.trim().toUpperCase()).filter(s => s);
        
        if (symbolList.length === 0) {
            this.showAlert('请输入有效的股票代码', 'warning');
            return;
        }

        this.watchedSymbols = symbolList;
        await this.loadStockData();
    }

    async loadStockData() {
        if (this.watchedSymbols.length === 0) {
            return;
        }

        try {
            this.showLoading(true);
            this.showRefreshIndicator(true);

            const response = await fetch('/api/market-data/realtime', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    symbols: this.watchedSymbols,
                    market: this.currentMarket
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateStockData(data.data);
                document.getElementById('stockDataContainer').style.display = 'block';
            } else {
                this.showAlert(data.error || '获取股票数据失败', 'danger');
            }

        } catch (error) {
            console.error('获取股票数据失败:', error);
            this.showAlert('网络错误，请重试', 'danger');
        } finally {
            this.showLoading(false);
            this.showRefreshIndicator(false);
        }
    }

    updateStockData(stockData) {
        const container = document.getElementById('stockList');
        
        if (!stockData || stockData.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">未找到股票数据</p>';
            return;
        }

        container.innerHTML = stockData.map(stock => {
            const changeClass = stock.change >= 0 ? 'price-up' : 'price-down';
            const changeSign = stock.change >= 0 ? '+' : '';
            const borderClass = stock.change >= 0 ? '' : 'negative';
            
            return `
                <div class="stock-item ${borderClass}">
                    <div class="stock-header">
                        <div>
                            <div class="stock-symbol">${stock.symbol}</div>
                            <small class="text-muted">${this.getMarketName(stock.market)}</small>
                        </div>
                        <div class="text-end">
                            <div class="stock-price ${changeClass}">
                                ${this.formatCurrency(stock.price, stock.currency)}
                            </div>
                            <div class="${changeClass}">
                                ${changeSign}${this.formatNumber(stock.change)} 
                                (${changeSign}${stock.change_percent.toFixed(2)}%)
                            </div>
                        </div>
                    </div>
                    
                    <div class="stock-details">
                        <div class="detail-item">
                            <div class="detail-label">开盘</div>
                            <div class="detail-value">${this.formatNumber(stock.open)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">最高</div>
                            <div class="detail-value">${this.formatNumber(stock.high)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">最低</div>
                            <div class="detail-value">${this.formatNumber(stock.low)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">昨收</div>
                            <div class="detail-value">${this.formatNumber(stock.close)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">成交量</div>
                            <div class="detail-value">${this.formatVolume(stock.volume)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">更新时间</div>
                            <div class="detail-value">${new Date(stock.timestamp).toLocaleTimeString()}</div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    toggleAutoRefresh() {
        const button = document.getElementById('autoRefreshToggle');
        
        if (this.autoRefresh) {
            this.stopAutoRefresh();
            button.classList.remove('active');
            button.innerHTML = '<i class="fas fa-sync-alt"></i> 自动刷新';
        } else {
            this.startAutoRefresh();
            button.classList.add('active');
            button.innerHTML = '<i class="fas fa-pause"></i> 停止刷新';
        }
    }

    startAutoRefresh() {
        this.autoRefresh = true;
        this.refreshTimer = setInterval(() => {
            if (this.watchedSymbols.length > 0) {
                this.loadStockData();
            }
            this.loadMarketOverview();
        }, this.refreshInterval);
    }

    stopAutoRefresh() {
        this.autoRefresh = false;
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }

    showRefreshIndicator(show) {
        const indicator = document.getElementById('refreshIndicator');
        indicator.style.display = show ? 'block' : 'none';
    }

    // 工具方法
    formatNumber(value) {
        if (typeof value !== 'number') return value;
        return value.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    formatCurrency(value, currency) {
        if (typeof value !== 'number') return value;
        
        const symbols = {
            'USD': '$',
            'CNY': '¥',
            'HKD': 'HK$',
            'USDT': '$'
        };
        
        const symbol = symbols[currency] || '';
        return `${symbol}${this.formatNumber(value)}`;
    }

    formatVolume(volume) {
        if (typeof volume !== 'number') return volume;
        
        if (volume >= 100000000) {
            return `${(volume / 100000000).toFixed(1)}亿`;
        } else if (volume >= 10000) {
            return `${(volume / 10000).toFixed(1)}万`;
        } else {
            return volume.toLocaleString();
        }
    }

    getMarketName(market) {
        const names = {
            'cn': 'A股',
            'us': '美股',
            'hk': '港股',
            'crypto': '加密货币'
        };
        return names[market] || market;
    }

    showAlert(message, type = 'info') {
        // 创建alert元素
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.position = 'fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.left = '50%';
        alertDiv.style.transform = 'translateX(-50%)';
        alertDiv.style.zIndex = '9999';
        alertDiv.style.minWidth = '300px';
        
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
}

// 全局函数
function switchMarket(market) {
    marketDataManager.switchMarket(market);
}

function searchStocks() {
    marketDataManager.searchStocks();
}

function toggleAutoRefresh() {
    marketDataManager.toggleAutoRefresh();
}

// 初始化市场数据管理器
const marketDataManager = new MarketDataManager();
