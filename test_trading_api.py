#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实盘交易API测试脚本
"""

import requests
import json
import time

class TradingAPITester:
    def __init__(self, base_url="http://localhost:5001"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def login(self, username="admin", password="admin123"):
        """登录系统"""
        try:
            response = self.session.post(f"{self.base_url}/api/login", json={
                "username": username,
                "password": password
            })
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 登录成功")
                    return True
                else:
                    print(f"❌ 登录失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 登录请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def test_get_accounts(self):
        """测试获取交易账户"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/accounts")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取账户成功")
                    print(f"   账户数量: {data.get('total', 0)}")
                    for account in data.get('accounts', []):
                        print(f"   账户ID: {account['account_id']}")
                        print(f"   券商: {account['broker']}")
                        print(f"   总资产: ${account['total_value']:,.2f}")
                        print(f"   现金余额: ${account['balance']:,.2f}")
                    return True
                else:
                    print(f"❌ 获取账户失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取账户请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取账户异常: {e}")
            return False
    
    def test_get_portfolio(self, account_id="MOCK_001"):
        """测试获取投资组合摘要"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/portfolio/{account_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取投资组合成功")
                    summary = data.get('summary', {})
                    print(f"   总资产: ${summary.get('total_value', 0):,.2f}")
                    print(f"   现金余额: ${summary.get('cash_balance', 0):,.2f}")
                    print(f"   持仓市值: ${summary.get('positions_value', 0):,.2f}")
                    print(f"   未实现盈亏: ${summary.get('unrealized_pnl', 0):,.2f}")
                    print(f"   总收益率: {summary.get('total_return_percent', 0):.2f}%")
                    return True
                else:
                    print(f"❌ 获取投资组合失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取投资组合请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取投资组合异常: {e}")
            return False
    
    def test_create_order(self, symbol="AAPL", side="buy", quantity=10, order_type="market"):
        """测试创建订单"""
        try:
            order_data = {
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "order_type": order_type,
                "account_id": "MOCK_001"
            }
            
            response = self.session.post(f"{self.base_url}/api/trading/orders", json=order_data)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 创建订单成功")
                    print(f"   订单ID: {data.get('order_id')}")
                    print(f"   状态: {data.get('status')}")
                    return data.get('order_id')
                else:
                    print(f"❌ 创建订单失败: {data.get('error')}")
                    return None
            else:
                print(f"❌ 创建订单请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 创建订单异常: {e}")
            return None
    
    def test_get_orders(self, account_id="MOCK_001"):
        """测试获取订单列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/orders?account_id={account_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取订单列表成功")
                    print(f"   订单数量: {data.get('total', 0)}")
                    for order in data.get('orders', []):
                        print(f"   订单ID: {order['order_id'][:8]}...")
                        print(f"   股票: {order['symbol']}")
                        print(f"   方向: {order['side']}")
                        print(f"   数量: {order['quantity']}")
                        print(f"   状态: {order['status']}")
                        print("   ---")
                    return True
                else:
                    print(f"❌ 获取订单列表失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取订单列表请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取订单列表异常: {e}")
            return False
    
    def test_get_positions(self, account_id="MOCK_001"):
        """测试获取持仓信息"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/positions/{account_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取持仓信息成功")
                    print(f"   持仓数量: {data.get('total', 0)}")
                    for position in data.get('positions', []):
                        print(f"   股票: {position['symbol']}")
                        print(f"   数量: {position['quantity']}")
                        print(f"   成本价: ${position['avg_cost']:.2f}")
                        print(f"   市价: ${position['market_price']:.2f}")
                        print(f"   盈亏: ${position['unrealized_pnl']:.2f}")
                        print("   ---")
                    return True
                else:
                    print(f"❌ 获取持仓信息失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取持仓信息请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取持仓信息异常: {e}")
            return False
    
    def test_get_risk_metrics(self, account_id="MOCK_001"):
        """测试获取风险指标"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/risk/{account_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取风险指标成功")
                    metrics = data.get('metrics', {})
                    print(f"   1日VaR: ${metrics.get('var_1d', 0):,.2f}")
                    print(f"   最大回撤: {metrics.get('max_drawdown', 0)*100:.2f}%")
                    print(f"   当前回撤: {metrics.get('current_drawdown', 0)*100:.2f}%")
                    print(f"   波动率: {metrics.get('volatility', 0)*100:.2f}%")
                    print(f"   集中度风险: {metrics.get('concentration_risk', 0)*100:.2f}%")
                    return True
                else:
                    print(f"❌ 获取风险指标失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取风险指标请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取风险指标异常: {e}")
            return False
    
    def test_get_alerts(self):
        """测试获取风险预警"""
        try:
            response = self.session.get(f"{self.base_url}/api/trading/alerts")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print("✅ 获取风险预警成功")
                    print(f"   预警数量: {data.get('total', 0)}")
                    for alert in data.get('alerts', []):
                        print(f"   预警类型: {alert['alert_type']}")
                        print(f"   风险等级: {alert['risk_level']}")
                        print(f"   消息: {alert['message']}")
                        print("   ---")
                    return True
                else:
                    print(f"❌ 获取风险预警失败: {data.get('error')}")
                    return False
            else:
                print(f"❌ 获取风险预警请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取风险预警异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始实盘交易API测试")
        print("=" * 50)
        
        # 登录
        if not self.login():
            print("❌ 登录失败，终止测试")
            return
        
        print("\n📊 测试交易功能...")
        print("-" * 30)
        
        # 测试获取账户
        self.test_get_accounts()
        print()
        
        # 测试获取投资组合
        self.test_get_portfolio()
        print()
        
        # 测试创建订单
        order_id = self.test_create_order()
        print()
        
        # 等待订单处理
        if order_id:
            print("⏳ 等待订单处理...")
            time.sleep(3)
            print()
        
        # 测试获取订单列表
        self.test_get_orders()
        print()
        
        # 测试获取持仓
        self.test_get_positions()
        print()
        
        # 测试获取风险指标
        self.test_get_risk_metrics()
        print()
        
        # 测试获取风险预警
        self.test_get_alerts()
        print()
        
        print("✅ 所有测试完成")

if __name__ == "__main__":
    tester = TradingAPITester()
    tester.run_all_tests()
