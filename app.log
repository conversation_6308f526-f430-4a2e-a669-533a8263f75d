INFO:__main__:API配置模块加载成功
INFO:__main__:支付服务模块加载成功
INFO:cache_manager:Redis缓存连接成功
INFO:db_optimizer:数据库连接池创建成功: 2-20 连接
INFO:__main__:性能优化模块加载成功
INFO:database_manager:PostgreSQL连接成功
INFO:database_manager:Redis连接成功
INFO:__main__:数据库管理器模块加载成功
INFO:__main__:蓝图注册成功
INFO:__main__:Redis连接成功
INFO:__main__:PostgreSQL连接成功
INFO:__main__:实时数据服务已集成统一API
INFO:services.trading_service:模拟交易环境初始化完成
INFO:__main__:实盘交易服务模块导入成功
INFO:__main__:报表服务模块导入成功
INFO:__main__:AI服务模块导入成功
INFO:__main__:股票预测服务模块导入成功
/www/wwwroot/www.gdpp.com/venv/lib/python3.10/site-packages/py_mini_racer/py_mini_racer.py:15: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
INFO:services.data_source_manager:akshare数据源初始化成功
INFO:__main__:数据源管理服务模块导入成功
INFO:__main__:实时数据服务已启动
INFO:__main__:启动交易服务...
INFO:services.trading_service:交易服务已启动
INFO:services.risk_manager:风险监控已启动
INFO:__main__:交易服务启动成功
INFO:performance_monitor:性能监控已启动，监控间隔: 30秒
INFO:__main__:执行启动时数据库优化...
INFO:db_optimizer:开始数据库优化...
INFO:db_optimizer:索引创建成功: users_username
INFO:db_optimizer:索引创建成功: users_email
INFO:db_optimizer:索引创建成功: users_role
INFO:db_optimizer:索引创建成功: users_premium
INFO:db_optimizer:索引创建成功: users_created_at
INFO:db_optimizer:索引创建成功: watchlist_user_id
INFO:db_optimizer:索引创建成功: watchlist_symbol
INFO:db_optimizer:索引创建成功: watchlist_type
INFO:db_optimizer:索引创建成功: users_role_premium
INFO:db_optimizer:索引创建成功: watchlist_user_symbol
INFO:db_optimizer:索引创建完成: 10/10
INFO:db_optimizer:表分析完成: users
INFO:db_optimizer:表分析完成: user_watchlists
INFO:db_optimizer:VACUUM ANALYZE 执行完成
INFO:db_optimizer:数据库优化完成: {'indexes_created': 10, 'tables_analyzed': 2, 'vacuum_completed': True, 'stats_updated': True}
INFO:__main__:数据库优化完成: {'indexes_created': 10, 'tables_analyzed': 2, 'vacuum_completed': True, 'stats_updated': True}
INFO:__main__:执行缓存预热...
INFO:cache_manager:开始缓存预热...
INFO:user_manager:用户管理器数据库连接成功
INFO:user_manager:用户管理器数据库连接成功
INFO:user_manager:获取用户列表成功，共 10 个用户
INFO:cache_manager:缓存预热完成: 10 个用户
INFO:__main__:性能优化模块初始化完成
 * Serving Flask app 'app'
 * Debug mode: on
INFO:werkzeug:[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://***********:5001
INFO:werkzeug:[33mPress CTRL+C to quit[0m
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:24:51] "GET / HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:33:49] "[32mGET /market-data HTTP/1.1[0m" 302 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:35:02] "GET / HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:36:02] "GET / HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:43:14] "GET / HTTP/1.1" 200 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:43:14] "[32mGET /stock-prediction HTTP/1.1[0m" 302 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:43:14] "[32mGET /market-data HTTP/1.1[0m" 302 -
INFO:werkzeug:127.0.0.1 - - [31/May/2025 07:44:02] "GET / HTTP/1.1" 200 -
