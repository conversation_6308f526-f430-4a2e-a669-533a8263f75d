INFO:__main__:API配置模块加载成功
INFO:__main__:支付服务模块加载成功
INFO:cache_manager:Redis缓存连接成功
INFO:db_optimizer:数据库连接池创建成功: 2-20 连接
INFO:__main__:性能优化模块加载成功
INFO:database_manager:PostgreSQL连接成功
INFO:database_manager:Redis连接成功
INFO:__main__:数据库管理器模块加载成功
INFO:__main__:蓝图注册成功
INFO:__main__:Redis连接成功
INFO:__main__:PostgreSQL连接成功
INFO:__main__:实时数据服务已集成统一API
INFO:services.trading_service:模拟交易环境初始化完成
INFO:__main__:实盘交易服务模块导入成功
INFO:__main__:报表服务模块导入成功
INFO:__main__:AI服务模块导入成功
INFO:__main__:实时数据服务已启动
INFO:__main__:启动交易服务...
INFO:services.trading_service:交易服务已启动
INFO:services.risk_manager:风险监控已启动
INFO:services.broker_api:券商已添加: ib
INFO:services.broker_api:券商已添加: futu
INFO:services.broker_api:券商已添加: snowball
INFO:services.broker_api:默认券商配置初始化完成
INFO:services.broker_api:正在连接到Interactive Brokers...
INFO:ib_insync.client:Connecting to 127.0.0.1:7497 with clientId 1...
INFO:ib_insync.client:Disconnecting
ERROR:ib_insync.client:API connection failed: ConnectionRefusedError(111, "Connect call failed ('127.0.0.1', 7497)")
ERROR:ib_insync.client:Make sure API port on TWS/IBG is open
WARNING:services.broker_api:IB连接失败，启用模拟模式: [Errno 111] Connect call failed ('127.0.0.1', 7497)
INFO:services.broker_api:券商 ib 连接结果: True
INFO:services.broker_api:正在连接到富途证券...
[0;33m2025-05-31 06:50:37,826 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=1; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:50:45,828 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=2; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:50:53,836 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=3; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:01,845 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=4; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:09,852 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=5; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:17,861 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=6; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:25,866 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=7; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:33,869 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=8; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:41,878 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=9; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:49,887 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=10; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:51:57,896 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=11; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:05,898 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=12; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:13,907 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=13; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:21,910 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=14; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:29,917 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=15; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:37,926 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=16; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:45,934 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=17; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:52:53,937 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=18; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:01,942 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=19; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:09,951 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=20; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:17,960 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=21; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:25,969 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=22; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:33,972 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=23; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:41,981 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=24; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:49,990 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=25; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:53:57,996 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=26; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:06,002 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=27; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:14,011 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=28; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:22,019 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=29; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:30,028 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=30; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:38,037 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=31; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:46,046 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=32; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:54:54,055 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=33; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:02,060 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=34; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:10,069 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=35; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:18,078 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=36; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:26,087 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=37; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:34,095 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=38; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:42,102 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=39; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:50,108 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=40; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:55:58,114 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=41; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:06,122 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=42; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:14,130 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=43; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:22,133 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=44; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:30,141 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=45; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:38,150 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=46; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:46,158 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=47; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:56:54,167 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=48; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:02,169 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=49; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:10,177 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=50; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:18,186 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=51; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:26,194 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=52; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:34,202 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=53; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:42,209 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=54; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:50,218 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=55; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:57:58,226 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=56; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:06,234 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=57; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:14,240 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=58; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:22,244 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=59; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:30,250 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=60; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:38,259 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=61; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:46,268 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=62; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:58:54,274 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=63; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:02,283 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=64; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:10,290 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=65; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:18,299 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=66; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:26,307 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=67; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:34,314 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=68; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:42,320 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=69; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:50,326 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=70; msg=ECONNREFUSED[0m
[0;33m2025-05-31 06:59:58,333 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=71; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:06,342 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=72; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:14,349 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=73; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:22,357 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=74; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:30,362 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=75; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:38,368 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=76; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:46,376 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=77; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:00:54,383 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=78; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:02,392 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=79; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:10,400 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=80; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:18,406 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=81; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:26,410 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=82; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:34,418 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=83; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:42,426 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=84; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:50,429 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=85; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:01:58,438 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=86; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:06,446 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=87; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:14,455 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=88; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:22,463 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=89; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:30,470 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=90; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:38,478 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=91; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:46,486 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=92; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:02:54,495 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=93; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:02,503 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=94; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:10,512 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=95; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:18,519 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=96; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:26,527 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=97; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:34,534 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=98; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:42,543 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=99; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:50,547 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=100; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:03:58,551 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=101; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:06,560 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=102; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:14,569 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=103; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:22,577 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=104; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:30,583 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=105; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:38,591 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=106; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:46,600 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=107; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:04:54,609 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=108; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:02,614 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=109; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:10,621 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=110; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:18,630 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=111; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:26,638 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=112; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:34,647 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=113; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:42,656 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=114; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:50,662 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=115; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:05:58,670 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=116; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:06,674 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=117; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:14,679 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=118; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:22,685 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=119; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:30,691 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=120; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:38,697 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=121; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:46,702 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=122; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:06:54,706 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=123; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:02,713 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=124; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:10,722 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=125; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:18,731 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=126; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:26,740 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=127; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:34,746 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=128; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:42,755 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=129; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:50,763 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=130; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:07:58,769 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=131; msg=ECONNREFUSED[0m
[0;33m2025-05-31 07:08:06,777 | 62769 | [open_context_base.py] _connect_sync:287: Connect fail: conn_id=132; msg=ECONNREFUSED[0m
